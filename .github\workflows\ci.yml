name: CI/CD Pipeline

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Setup database
      run: |
        npx prisma generate
        npx prisma db push
      env:
        DATABASE_URL: "file:./test.db"
        
    - name: Run linting
      run: npm run lint
      
    - name: Run type checking
      run: npm run typecheck
      
    - name: Run unit tests
      run: npm run test:run
      
    - name: Run test coverage
      run: npm run test:coverage
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella
        
    - name: Build application
      run: npm run build
      env:
        DATABASE_URL: "file:./test.db"

  e2e-test:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Install Playwright browsers
      run: npx playwright install --with-deps
      
    - name: Setup database
      run: |
        npx prisma generate
        npx prisma db push
      env:
        DATABASE_URL: "file:./test.db"
        
    - name: Build application
      run: npm run build
      env:
        DATABASE_URL: "file:./test.db"
        
    - name: Run E2E tests
      run: npm run test:e2e
      env:
        DATABASE_URL: "file:./test.db"
        
    - name: Upload E2E test results
      uses: actions/upload-artifact@v3
      if: failure()
      with:
        name: playwright-report
        path: playwright-report/
        retention-days: 30

  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run security audit
      run: npm audit --audit-level=moderate
      
    - name: Run dependency check
      run: npx audit-ci --moderate

  deploy:
    runs-on: ubuntu-latest
    needs: [test, e2e-test, security-scan]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Setup database
      run: npx prisma generate
      env:
        DATABASE_URL: ${{ secrets.DATABASE_URL }}
        
    - name: Build application
      run: npm run build
      env:
        DATABASE_URL: ${{ secrets.DATABASE_URL }}
        GOOGLE_AI_API_KEY: ${{ secrets.GOOGLE_AI_API_KEY }}
        
    - name: Deploy to production
      run: echo "Deploy to your preferred platform"
      # Add your deployment commands here
