# Requirements Document

## Introduction

This feature adds a comprehensive ELO rating system for LLM profiles with tournament management, automated scheduling, and match history tracking to the Chess Duel Arena application. The system will allow users to manage multiple LLM profiles, each with their own ELO ratings, schedule tournaments between different models, and track performance over time through automated matches.

## Requirements

### Requirement 1

**User Story:** As a user, I want to manage LLM profiles with ELO ratings, so that I can track the performance of different AI models over time.

#### Acceptance Criteria

1. WHEN the application starts THEN the system SHALL display a sidebar with LLM profile management
2. WHEN a new LLM profile is created THEN the system SHALL assign an initial ELO rating of 1900
3. WHEN viewing LLM profiles THEN the system SHALL display current ELO rating, total games played, wins, losses, and draws
4. IF an LLM profile has played fewer than 10 games THEN the system SHALL mark it as in "volatile" period
5. WHEN editing an LLM profile THEN the system SHALL allow updating the model name and configuration

### Requirement 2

**User Story:** As a user, I want an ELO rating system that adjusts based on opponent strength and game experience, so that ratings accurately reflect model performance.

#### Acceptance Criteria

1. WHEN a match is completed THEN the system SHALL calculate ELO changes using the following formula: ΔElo = K × (Result - Expected)
2. IF a player has played fewer than 5 games THEN the system SHALL use K-factor of 100 (volatile period)
3. IF a player has played 5-9 games THEN the system SHALL use K-factor of 50 (stabilizing period)
4. IF a player has played 10 or more games THEN the system SHALL use K-factor of 20 (stable period)
5. WHEN calculating expected score THEN the system SHALL use: Expected = 1 / (1 + 10^((OpponentElo - PlayerElo) / 400))
6. WHEN a game result is win THEN Result = 1, IF draw THEN Result = 0.5, IF loss THEN Result = 0
7. WHEN ELO difference is 0 and both players are stable (10+ games) THEN maximum gain/loss SHALL be ±10 points
8. WHEN ELO difference is 200 points (e.g., 1900 vs 1700) THEN lower-rated player gains ~15 points for win and loses ~5 points for loss
9. WHEN ELO difference is 200 points (e.g., 1900 vs 1700) THEN higher-rated player gains ~5 points for win and loses ~15 points for loss

### Requirement 3

**User Story:** As a user, I want to create and manage tournaments with automated scheduling, so that I can run systematic competitions between LLM models.

#### Acceptance Criteria

1. WHEN creating a tournament THEN the system SHALL allow selecting participating LLM profiles
2. WHEN creating a tournament THEN the system SHALL allow setting tournament format (round-robin, single elimination, double elimination)
3. WHEN a tournament is created THEN the system SHALL generate the match schedule automatically
4. IF tournament format is elimination THEN the system SHALL only advance to next round when all current round matches are complete
5. WHEN viewing tournaments THEN the system SHALL display current status, participating models, and bracket/standings

### Requirement 4

**User Story:** As a user, I want automated match execution with scheduling, so that tournaments can run without manual intervention.

#### Acceptance Criteria

1. WHEN a tournament is scheduled THEN the system SHALL execute matches automatically at specified intervals
2. WHEN a match is in progress THEN the system SHALL prevent other matches from starting with the same models
3. IF a match encounters an error THEN the system SHALL log the error and mark the match as failed
4. WHEN all matches in a tournament round are complete THEN the system SHALL automatically advance to the next round
5. WHEN a tournament is complete THEN the system SHALL update all participant ELO ratings

### Requirement 5

**User Story:** As a user, I want to view comprehensive match history and statistics, so that I can analyze model performance over time.

#### Acceptance Criteria

1. WHEN viewing match history THEN the system SHALL display all completed matches with date, participants, result, and ELO changes
2. WHEN filtering match history THEN the system SHALL allow filtering by model, date range, and tournament
3. WHEN viewing model statistics THEN the system SHALL display win rate, average opponent ELO, performance trends
4. WHEN exporting match data THEN the system SHALL provide options to export in JSON or CSV format
5. WHEN viewing a specific match THEN the system SHALL display the complete game PGN and move history

### Requirement 6

**User Story:** As a user, I want an intuitive sidebar navigation system, so that I can easily access profiles, tournaments, and match history.

#### Acceptance Criteria

1. WHEN the application loads THEN the system SHALL display a sidebar with navigation sections for Profiles, Tournaments, and History
2. WHEN clicking on a sidebar section THEN the system SHALL navigate to the corresponding view
3. WHEN in the Profiles section THEN the system SHALL display all LLM profiles with their current ELO and status
4. WHEN in the Tournaments section THEN the system SHALL display active, scheduled, and completed tournaments
5. WHEN in the History section THEN the system SHALL display recent matches and provide filtering options
6. WHEN the sidebar is collapsed THEN the system SHALL show icon-only navigation with tooltips

### Requirement 7

**User Story:** As a user, I want to schedule tournaments with flexible timing options, so that I can control when automated matches occur.

#### Acceptance Criteria

1. WHEN scheduling a tournament THEN the system SHALL allow setting start time, match intervals, and daily time windows
2. WHEN a scheduled tournament time arrives THEN the system SHALL automatically start the next available match
3. IF no models are available for a scheduled match THEN the system SHALL wait and retry at the next interval
4. WHEN pausing a tournament THEN the system SHALL stop automatic match execution but preserve the schedule
5. WHEN resuming a tournament THEN the system SHALL continue from where it was paused