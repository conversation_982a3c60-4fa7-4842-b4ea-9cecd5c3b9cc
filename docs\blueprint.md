# **App Name**: Chess Duel Arena

## Core Features:

- Real-time Chessboard: Display an interactive chessboard that visualizes the game in real-time using `chess.js`.
- Model Selection: Choose the Gemini model for white and black.
- State Visualization: Show the current state of the game in PGN (Portable Game Notation) and FEN (Forsyth–Edwards Notation) formats.
- Game Reset: Restart or reset a running match between two models. 
- Reasoning-based Draw Offers: Enable or disable the reasoning tool; when the reasoning tool is enabled, the selected LLM decides, after considering a number of factors such as board position and game history, whether or not to offer the opponent a draw.

## Style Guidelines:

- Primary color: Deep navy blue (#34495E) to convey a sense of intelligence and depth.
- Background color: Light gray (#ECF0F1) with low saturation (20%) and high brightness to maintain a clean, unobtrusive backdrop.
- Accent color: Muted blue (#4B77BE) for interactive elements and highlights, differentiating it in both brightness and saturation from the primary color.
- Body and headline font: 'Inter', a grotesque-style sans-serif known for its modern, machined, neutral look that ensures readability and clarity.
- Use crisp, minimalist icons for controls.
- Chessboard to take center layout, with model selectors above and game state info below.