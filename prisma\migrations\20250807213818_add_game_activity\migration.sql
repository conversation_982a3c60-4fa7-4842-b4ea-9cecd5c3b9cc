-- CreateTable
CREATE TABLE "public"."GameActivity" (
    "id" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "whitePlayer" TEXT NOT NULL,
    "blackPlayer" TEXT NOT NULL,
    "whiteElo" INTEGER,
    "blackElo" INTEGER,
    "whiteEloChange" INTEGER,
    "blackEloChange" INTEGER,
    "result" TEXT,
    "move" TEXT,
    "reasoning" TEXT,
    "gameId" TEXT,
    "metadata" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "GameActivity_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "GameActivity_gameId_idx" ON "public"."GameActivity"("gameId");

-- CreateIndex
CREATE INDEX "GameActivity_type_idx" ON "public"."GameActivity"("type");

-- CreateIndex
CREATE INDEX "GameActivity_createdAt_idx" ON "public"."GameActivity"("createdAt");
