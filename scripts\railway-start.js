#!/usr/bin/env node

/**
 * Railway-specific startup script
 * Ensures proper database initialization and connection health before starting the app
 */

const { execSync } = require('child_process');
const { PrismaClient } = require('@prisma/client');

const MAX_RETRIES = 5;
const RETRY_DELAY = 2000;

async function waitForDatabase() {
  console.log('🔍 Checking database connectivity...');
  
  for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
    try {
      const prisma = new PrismaClient({
        datasources: {
          db: {
            url: process.env.DATABASE_URL,
          },
        },
      });
      
      await prisma.$queryRaw`SELECT 1`;
      await prisma.$disconnect();
      
      console.log('✅ Database connection successful');
      return true;
    } catch (error) {
      console.log(`❌ Database connection attempt ${attempt}/${MAX_RETRIES} failed:`, error.message);
      
      if (attempt === MAX_RETRIES) {
        console.error('💥 Failed to connect to database after all retries');
        process.exit(1);
      }
      
      console.log(`⏳ Waiting ${RETRY_DELAY}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
    }
  }
}

async function runMigrations() {
  console.log('🚀 Running database migrations...');
  
  try {
    execSync('npx prisma migrate deploy', {
      stdio: 'inherit',
      env: process.env,
    });
    console.log('✅ Database migrations completed');
  } catch (error) {
    console.error('❌ Database migrations failed:', error.message);
    process.exit(1);
  }
}

async function startApplication() {
  console.log('🎯 Starting Next.js application...');
  
  try {
    execSync('next start', {
      stdio: 'inherit',
      env: process.env,
    });
  } catch (error) {
    console.error('❌ Application startup failed:', error.message);
    process.exit(1);
  }
}

async function main() {
  console.log('🚂 Railway startup script initiated');
  console.log('📊 Environment:', {
    NODE_ENV: process.env.NODE_ENV,
    RAILWAY_ENVIRONMENT: process.env.RAILWAY_ENVIRONMENT,
    RAILWAY_DEPLOYMENT_ID: process.env.RAILWAY_DEPLOYMENT_ID,
  });
  
  try {
    await waitForDatabase();
    await runMigrations();
    await startApplication();
  } catch (error) {
    console.error('💥 Startup failed:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 Received SIGTERM, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 Received SIGINT, shutting down gracefully');
  process.exit(0);
});

main().catch((error) => {
  console.error('💥 Unhandled error in startup script:', error);
  process.exit(1);
});
