import {genkit} from 'genkit';
import {googleAI} from '@genkit-ai/googleai';
import {openAICompatible} from '@genkit-ai/compat-oai';

export const ai = genkit({
  plugins: [
    googleAI(),
    openAICompatible({
      name: 'openrouter',
      apiKey: process.env.OPENROUTER_API_KEY, // OpenRouter API key
      baseURL: 'https://openrouter.ai/api/v1', // OpenRouter base URL
    })
  ],
  model: 'googleai/gemini-2.0-flash',
});
