
'use server';

import prisma from '@/lib/db';
import { z } from 'zod';
import { revalidatePath } from 'next/cache';

const SaveGameSchema = z.object({
  white: z.string(),
  black: z.string(),
  result: z.enum(['1-0', '0-1', '1/2-1/2', '*']),
  pgn: z.string(),
});

type SaveGameInput = z.infer<typeof SaveGameSchema>;

export async function saveGame(data: SaveGameInput) {
  try {
    const validatedData = SaveGameSchema.parse(data);
    const savedGame = await prisma.game.create({
      data: validatedData,
    });
    revalidatePath('/');
    return savedGame;
  } catch (error) {
    console.error('Failed to save game:', error);
    throw new Error('Could not save the game to the database.');
  }
}

export async function getGames() {
  try {
    const games = await prisma.game.findMany({
      orderBy: {
        createdAt: 'desc',
      },
    });
    // Convert Date objects to strings
    return games.map(game => ({
      ...game,
      createdAt: game.createdAt.toISOString(),
    }));
  } catch (error) {
    console.error('Failed to fetch games:', error);
    // This is a server-side function, throwing an error is appropriate
    // The client will catch it and display a message.
    throw new Error('Could not retrieve games from the database.');
  }
}

export async function deleteGame(id: string) {
  try {
    const deletedGame = await prisma.game.delete({
      where: { id },
    });
    revalidatePath('/');
    return deletedGame;
  } catch (error) {
    console.error(`Failed to delete game ${id}:`, error);
    throw new Error('Could not delete the game from the database.');
  }
}

    