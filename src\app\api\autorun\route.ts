// src/app/api/autorun/route.ts
import { NextResponse, type NextRequest } from 'next/server';
import { Chess } from 'chess.js';
import prisma from '@/lib/db';
import { generateMove } from '@/ai/flows/generate-chess-move';
import { sendUpdate } from '../spectate/[gameId]/route';

export const dynamic = 'force-dynamic';

type Player = 'white' | 'black';

async function runGame(gameId: string, whiteModel: string, blackModel: string) {
  const game = new Chess();
  let finalResult: '1-0' | '0-1' | '1/2-1/2' | '*' = '*';
  let gameStatus: 'COMPLETED' | 'FAILED' = 'COMPLETED';
  let reasoningHistory: ReasoningHistoryEntry[] = [];

  console.log(`AUTORUN LIVE: Starting game ${gameId} between ${whiteModel} and ${blackModel}`);

  game.header('Event', `Autorun: ${whiteModel} vs ${blackModel}`);
  game.header('Site', 'Chess Duel Arena Backend');
  game.header('Date', new Date().toISOString().split('T')[0]);
  game.header('Round', '1');
  game.header('White', whiteModel);
  game.header('Black', blackModel);

  try {
    while (!game.isGameOver()) {
      const currentPlayer: Player = game.turn() === 'w' ? 'white' : 'black';
      const currentModel = currentPlayer === 'white' ? whiteModel : blackModel;
      let moveSuccessful = false;
      let attempts = 0;
      const maxAttempts = 5;

      while (attempts < maxAttempts && !moveSuccessful) {
        attempts++;
        try {
          const legalMoves = game.moves({ verbose: false });
          const aiResult = await generateMove({
            fen: game.fen(),
            pgn: game.pgn(),
            player: currentPlayer,
            reasoningMode: true,
            model: currentModel,
            legalMoves: legalMoves,
            isChess960: false,
          });

          if (aiResult && aiResult.move && game.move(aiResult.move)) {
            moveSuccessful = true;
            
            // Store reasoning data
            reasoningHistory.push({
              moveNumber: game.moveNumber(),
              player: currentPlayer,
              move: aiResult.move,
              reasoning: aiResult.reasoning,
              reason: aiResult.reason,
              analysis: aiResult.analysis,
              opponentPrediction: aiResult.opponentPrediction
            });

            // After a successful move, update the database and notify spectators
            const currentPgn = game.pgn();
            try {
              await prisma.game.update({
                where: { id: gameId },
                data: { 
                  pgn: currentPgn,
                  reasoningHistory: reasoningHistory
                },
              });
              sendUpdate(gameId, currentPgn, 'IN_PROGRESS', aiResult);
            } catch (updateError) {
              console.error(`AUTORUN LIVE (${gameId}): Failed to update game in database:`, updateError);
              // Try to find and update the game, or create if it doesn't exist
              const existingGame = await prisma.game.findUnique({ where: { id: gameId } });
              if (!existingGame) {
                console.log(`AUTORUN LIVE (${gameId}): Game not found, creating new record`);
                await prisma.game.create({
                  data: {
                    id: gameId,
                    white: whiteModel,
                    black: blackModel,
                    pgn: currentPgn,
                    status: 'IN_PROGRESS',
                    reasoningHistory: reasoningHistory,
                  },
                });
              }
              sendUpdate(gameId, currentPgn, 'IN_PROGRESS');
            }
          } else {
            console.error(`AUTORUN LIVE (${gameId}): Invalid move '${aiResult.move}' from ${currentModel}. Attempt ${attempts}`);
          }
        } catch (error) {
          console.error(`AUTORUN LIVE (${gameId}): Error generating move for ${currentModel}. Attempt ${attempts}`, error);
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      if (!moveSuccessful) {
        console.error(`AUTORUN LIVE (${gameId}): ${currentModel} failed to make a valid move after ${maxAttempts} attempts. Aborting.`);
        finalResult = currentPlayer === 'white' ? '0-1' : '1-0';
        game.header('Result', finalResult);
        break;
      }
    }

    if (finalResult === '*') {
      if (game.isCheckmate()) {
        finalResult = game.turn() === 'b' ? '1-0' : '0-1';
      } else if (game.isDraw() || game.isStalemate() || game.isThreefoldRepetition() || game.isInsufficientMaterial()) {
        finalResult = '1/2-1/2';
      }
    }
    game.header('Result', finalResult);

  } catch (e) {
    console.error(`AUTORUN LIVE (${gameId}): A fatal error occurred during game execution.`, e);
    gameStatus = 'FAILED';
    finalResult = '*';
  } finally {
    const finalPgn = game.pgn();
    try {
      await prisma.game.update({
        where: { id: gameId },
        data: {
          pgn: finalPgn,
          status: gameStatus,
          result: finalResult,
          reasoningHistory: reasoningHistory,
        },
      });
    } catch (updateError) {
      console.error(`AUTORUN LIVE (${gameId}): Failed to update final game state:`, updateError);
      // Try to find and update the game, or create if it doesn't exist
      const existingGame = await prisma.game.findUnique({ where: { id: gameId } });
      if (!existingGame) {
        console.log(`AUTORUN LIVE (${gameId}): Game not found, creating new record for final state`);
        await prisma.game.create({
          data: {
            id: gameId,
            white: whiteModel,
            black: blackModel,
            pgn: finalPgn,
            status: gameStatus,
            result: finalResult,
            reasoningHistory: reasoningHistory,
          },
        });
      }
    }
    sendUpdate(gameId, finalPgn, gameStatus);
    console.log(`AUTORUN LIVE (${gameId}): Game finished. Result: ${finalResult}`);
  }
}

export async function POST(request: NextRequest) {
  console.log("AUTORUN LIVE: Received request to start a new game.");

  try {
    const body = await request.json();
    const whiteModel = body.whiteModel || process.env.AUTORUN_WHITE_MODEL || 'gemini-2.0-flash';
    const blackModel = body.blackModel || process.env.AUTORUN_BLACK_MODEL || 'gemini-2.5-pro';

    // 1. Create the game in the database immediately
    const newGame = await prisma.game.create({
      data: {
        white: whiteModel,
        black: blackModel,
        pgn: '', // Start with an empty PGN
        status: 'IN_PROGRESS',
      },
    });

    // 2. Start the game simulation in the background (fire-and-forget)
    runGame(newGame.id, whiteModel, blackModel).catch(error => {
      console.error(`AUTORUN LIVE (${newGame.id}): Background execution failed catastrophically.`, error);
      prisma.game.update({
        where: { id: newGame.id },
        data: { status: 'FAILED', result: '*' },
      }).catch(dbError => console.error(`AUTORUN LIVE (${newGame.id}): Could not even mark game as FAILED in DB.`, dbError));
    });

    // 3. Return the new game object to the client
    return NextResponse.json(newGame, { status: 202 });

  } catch (error) {
    console.error('AUTORUN LIVE: Invalid request body.', error);
    return new NextResponse('Bad Request: Invalid JSON body.', { status: 400 });
  }
}