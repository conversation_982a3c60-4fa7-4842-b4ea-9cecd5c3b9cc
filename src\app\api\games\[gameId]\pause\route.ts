import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/db';

// Pause/Stop a live game
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ gameId: string }> }
) {
  try {
    const { gameId } = await params;

    if (!gameId) {
      return NextResponse.json(
        { error: 'Game ID is required' },
        { status: 400 }
      );
    }

    // Find the game
    const game = await prisma.game.findUnique({
      where: { id: gameId },
    });

    if (!game) {
      return NextResponse.json(
        { error: 'Game not found' },
        { status: 404 }
      );
    }

    if (game.status !== 'IN_PROGRESS') {
      return NextResponse.json(
        { error: 'Only games in progress can be paused' },
        { status: 400 }
      );
    }

    // Update game status to STOPPED (effectively stopping it)
    const updatedGame = await prisma.game.update({
      where: { id: gameId },
      data: {
        status: 'STOPPED', // Game was manually stopped/cancelled
        result: '*', // Indicates game was stopped/cancelled
        updatedAt: new Date(),
      },
    });

    // Log the pause activity
    await prisma.gameActivity.create({
      data: {
        type: 'game_end',
        whitePlayer: game.white,
        blackPlayer: game.black,
        result: '*',
        gameId: gameId,
        metadata: JSON.stringify({ reason: 'Game manually stopped' }),
      },
    });

    return NextResponse.json({
      success: true,
      game: updatedGame,
      message: 'Game has been stopped successfully',
    });

  } catch (error) {
    console.error('Error pausing game:', error);
    return NextResponse.json(
      { error: 'Failed to pause game' },
      { status: 500 }
    );
  }
}