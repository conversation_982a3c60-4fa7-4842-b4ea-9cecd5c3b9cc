import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/db';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ gameId: string }> }
) {
  try {
    const { gameId } = await params;

    if (!gameId) {
      return NextResponse.json(
        { error: 'Game ID is required' },
        { status: 400 }
      );
    }

    const game = await prisma.game.findUnique({
      where: { id: gameId },
      include: {
        match: {
          include: {
            whiteProfile: true,
            blackProfile: true,
            tournament: true,
          },
        },
      },
    });

    if (!game) {
      return NextResponse.json(
        { error: 'Game not found' },
        { status: 404 }
      );
    }

    // Get game activities for this game
    const activities = await prisma.gameActivity.findMany({
      where: { gameId: gameId },
      orderBy: { createdAt: 'desc' },
      take: 100, // Limit to last 100 activities
    });

    // Transform game data
    const transformedGame = {
      id: game.id,
      white: game.white,
      black: game.black,
      result: game.result,
      pgn: game.pgn,
      status: game.status,
      createdAt: game.createdAt,
      updatedAt: game.updatedAt,
      moveHistory: game.moveHistory,
      gameLogs: game.gameLogs,
      // Additional computed fields
      isLive: game.status === 'IN_PROGRESS',
      duration: game.status !== 'IN_PROGRESS' 
        ? Math.round((new Date(game.updatedAt).getTime() - new Date(game.createdAt).getTime()) / 1000)
        : Math.round((new Date().getTime() - new Date(game.createdAt).getTime()) / 1000),
      moveCount: game.pgn ? game.pgn.split(' ').filter(move => 
        move.match(/^[1-9]\d*\./) || move.match(/^[a-h][1-8]/) || move.match(/^[NBRQK]/)
      ).length : 0,
      // Tournament info if available
      tournament: game.match?.tournament ? {
        id: game.match.tournament.id,
        name: game.match.tournament.name,
        format: game.match.tournament.format,
        status: game.match.tournament.status,
      } : null,
      // Profile info if available
      whiteProfile: game.match?.whiteProfile ? {
        id: game.match.whiteProfile.id,
        name: game.match.whiteProfile.name,
        model: game.match.whiteProfile.model,
        eloRating: game.match.whiteProfile.eloRating,
      } : null,
      blackProfile: game.match?.blackProfile ? {
        id: game.match.blackProfile.id,
        name: game.match.blackProfile.name,
        model: game.match.blackProfile.model,
        eloRating: game.match.blackProfile.eloRating,
      } : null,
      // Activities
      activities: activities.map(activity => ({
        id: activity.id,
        type: activity.type,
        whitePlayer: activity.whitePlayer,
        blackPlayer: activity.blackPlayer,
        whiteElo: activity.whiteElo,
        blackElo: activity.blackElo,
        whiteEloChange: activity.whiteEloChange,
        blackEloChange: activity.blackEloChange,
        result: activity.result,
        move: activity.move,
        reasoning: activity.reasoning,
        metadata: activity.metadata ? JSON.parse(activity.metadata) : null,
        createdAt: activity.createdAt,
      })),
    };

    return NextResponse.json(transformedGame);

  } catch (error) {
    console.error('Error fetching game:', error);
    return NextResponse.json(
      { error: 'Failed to fetch game' },
      { status: 500 }
    );
  }
}

// Update game (for updating status, result, etc.)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ gameId: string }> }
) {
  try {
    const { gameId } = await params;
    const body = await request.json();

    if (!gameId) {
      return NextResponse.json(
        { error: 'Game ID is required' },
        { status: 400 }
      );
    }

    const game = await prisma.game.update({
      where: { id: gameId },
      data: body,
    });

    return NextResponse.json(game);

  } catch (error) {
    console.error('Error updating game:', error);
    return NextResponse.json(
      { error: 'Failed to update game' },
      { status: 500 }
    );
  }
}

// Delete game
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ gameId: string }> }
) {
  try {
    const { gameId } = await params;

    if (!gameId) {
      return NextResponse.json(
        { error: 'Game ID is required' },
        { status: 400 }
      );
    }

    // Delete related activities first
    await prisma.gameActivity.deleteMany({
      where: { gameId: gameId },
    });

    // Delete the game
    await prisma.game.delete({
      where: { id: gameId },
    });

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error deleting game:', error);
    return NextResponse.json(
      { error: 'Failed to delete game' },
      { status: 500 }
    );
  }
}
