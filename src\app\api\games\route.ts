import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status'); // 'live', 'completed', 'all'
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');

    let whereClause: any = {};

    // Filter by status
    if (status === 'live') {
      whereClause.status = 'IN_PROGRESS';
    } else if (status === 'completed') {
      whereClause.status = { in: ['COMPLETED', 'FAILED'] };
    }
    // If status is 'all' or not specified, get all games

    const games = await prisma.game.findMany({
      where: whereClause,
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: offset,
      include: {
        match: {
          include: {
            whiteProfile: true,
            blackProfile: true,
            tournament: true,
          },
        },
      },
    });

    // Get total count for pagination
    const totalCount = await prisma.game.count({
      where: whereClause,
    });

    // Transform games to include additional info
    const transformedGames = games.map(game => ({
      id: game.id,
      white: game.white,
      black: game.black,
      result: game.result,
      pgn: game.pgn,
      status: game.status,
      createdAt: game.createdAt,
      updatedAt: game.updatedAt,
      moveHistory: game.moveHistory,
      gameLogs: game.gameLogs,
      // Additional computed fields
      isLive: game.status === 'IN_PROGRESS',
      duration: game.status !== 'IN_PROGRESS' 
        ? Math.round((new Date(game.updatedAt).getTime() - new Date(game.createdAt).getTime()) / 1000)
        : Math.round((new Date().getTime() - new Date(game.createdAt).getTime()) / 1000),
      moveCount: game.pgn ? game.pgn.split(' ').filter(move => 
        move.match(/^[1-9]\d*\./) || move.match(/^[a-h][1-8]/) || move.match(/^[NBRQK]/)
      ).length : 0,
      // Tournament info if available
      tournament: game.match?.tournament ? {
        id: game.match.tournament.id,
        name: game.match.tournament.name,
        format: game.match.tournament.format,
      } : null,
      // Profile info if available
      whiteProfile: game.match?.whiteProfile ? {
        id: game.match.whiteProfile.id,
        name: game.match.whiteProfile.name,
        eloRating: game.match.whiteProfile.eloRating,
      } : null,
      blackProfile: game.match?.blackProfile ? {
        id: game.match.blackProfile.id,
        name: game.match.blackProfile.name,
        eloRating: game.match.blackProfile.eloRating,
      } : null,
    }));

    return NextResponse.json({
      games: transformedGames,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount,
      },
    });

  } catch (error) {
    console.error('Error fetching games:', error);
    return NextResponse.json(
      { error: 'Failed to fetch games' },
      { status: 500 }
    );
  }
}

// Create a new game (for manual game creation)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { white, black, pgn = '', status = 'IN_PROGRESS' } = body;

    if (!white || !black) {
      return NextResponse.json(
        { error: 'White and black players are required' },
        { status: 400 }
      );
    }

    const game = await prisma.game.create({
      data: {
        white,
        black,
        pgn,
        status,
      },
    });

    return NextResponse.json(game, { status: 201 });

  } catch (error) {
    console.error('Error creating game:', error);
    return NextResponse.json(
      { error: 'Failed to create game' },
      { status: 500 }
    );
  }
}
