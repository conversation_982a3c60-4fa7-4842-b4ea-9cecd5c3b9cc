import { NextRequest, NextResponse } from 'next/server';
import { TournamentStatus } from '@prisma/client';
import db from '@/lib/db';
import { BackgroundJobSystem } from '@/lib/background-jobs';
import { TournamentService } from '@/lib/tournament-service';
import { TournamentEventEmitter } from '@/lib/tournament-webhooks';

export const dynamic = 'force-dynamic';

/**
 * Pause a tournament and stop automated match execution
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { tournamentId: string } }
) {
  try {
    const { tournamentId } = params;

    // Validate tournament exists and can be paused
    const tournament = await TournamentService.getTournamentById(tournamentId);
    if (!tournament) {
      return NextResponse.json(
        { error: 'Tournament not found' },
        { status: 404 }
      );
    }

    if (tournament.status !== TournamentStatus.ACTIVE) {
      return NextResponse.json(
        { error: `Cannot pause tournament with status: ${tournament.status}` },
        { status: 400 }
      );
    }

    // Update tournament status to PAUSED
    const updatedTournament = await TournamentService.updateTournamentStatus(
      tournamentId,
      TournamentStatus.PAUSED
    );

    // Stop the background job
    BackgroundJobSystem.stopTournamentJob(tournamentId);

    // Emit tournament paused event
    TournamentEventEmitter.tournamentPaused(tournamentId, {
      name: tournament.name,
    });

    return NextResponse.json({
      message: 'Tournament paused successfully',
      tournament: {
        id: updatedTournament.id,
        name: tournament.name,
        status: updatedTournament.status,
      },
      jobStatus: BackgroundJobSystem.getJobStatus(tournamentId),
    });

  } catch (error) {
    console.error('Error pausing tournament:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to pause tournament',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}