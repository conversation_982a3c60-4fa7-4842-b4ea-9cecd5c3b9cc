import { NextRequest, NextResponse } from 'next/server';
import { TournamentStatus } from '@prisma/client';
import db from '@/lib/db';
import { BackgroundJobSystem } from '@/lib/background-jobs';
import { TournamentService } from '@/lib/tournament-service';
import { TournamentEventEmitter } from '@/lib/tournament-webhooks';

export const dynamic = 'force-dynamic';

/**
 * Resume a paused tournament and restart automated match execution
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { tournamentId: string } }
) {
  try {
    const { tournamentId } = params;

    // Validate tournament exists and can be resumed
    const tournament = await TournamentService.getTournamentById(tournamentId);
    if (!tournament) {
      return NextResponse.json(
        { error: 'Tournament not found' },
        { status: 404 }
      );
    }

    if (tournament.status !== TournamentStatus.PAUSED) {
      return NextResponse.json(
        { error: `Cannot resume tournament with status: ${tournament.status}` },
        { status: 400 }
      );
    }

    // Update tournament status to ACTIVE
    const updatedTournament = await TournamentService.updateTournamentStatus(
      tournamentId,
      TournamentStatus.ACTIVE
    );

    // Restart the background job
    await BackgroundJobSystem.startTournamentJob(tournamentId);

    // Emit tournament resumed event
    TournamentEventEmitter.tournamentResumed(tournamentId, {
      name: tournament.name,
    });

    return NextResponse.json({
      message: 'Tournament resumed successfully',
      tournament: {
        id: updatedTournament.id,
        name: tournament.name,
        status: updatedTournament.status,
      },
      jobStatus: BackgroundJobSystem.getJobStatus(tournamentId),
    });

  } catch (error) {
    console.error('Error resuming tournament:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to resume tournament',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}