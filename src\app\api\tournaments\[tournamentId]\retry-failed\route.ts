import { NextRequest, NextResponse } from 'next/server';
import { TournamentStatus } from '@prisma/client';
import db from '@/lib/db';
import { MatchScheduler } from '@/lib/match-scheduler';
import { TournamentService } from '@/lib/tournament-service';

export const dynamic = 'force-dynamic';

/**
 * Retry all failed matches in a tournament
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { tournamentId: string } }
) {
  try {
    const { tournamentId } = params;

    // Validate tournament exists
    const tournament = await TournamentService.getTournamentById(tournamentId);
    if (!tournament) {
      return NextResponse.json(
        { error: 'Tournament not found' },
        { status: 404 }
      );
    }

    // Get current progress to show failed matches count
    const progressBefore = await MatchScheduler.getTournamentProgress(tournamentId);
    
    if (progressBefore.failedMatches === 0) {
      return NextResponse.json({
        message: 'No failed matches to retry',
        progress: progressBefore,
      });
    }

    // Retry failed matches
    const retriedCount = await MatchScheduler.retryFailedMatches(tournamentId);

    // Get updated progress
    const progressAfter = await MatchScheduler.getTournamentProgress(tournamentId);

    return NextResponse.json({
      message: `Successfully reset ${retriedCount} failed matches for retry`,
      retriedMatches: retriedCount,
      progressBefore: {
        failedMatches: progressBefore.failedMatches,
        scheduledMatches: progressBefore.scheduledMatches,
      },
      progressAfter: {
        failedMatches: progressAfter.failedMatches,
        scheduledMatches: progressAfter.scheduledMatches,
      },
    });

  } catch (error) {
    console.error('Error retrying failed matches:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to retry failed matches',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}