import { NextRequest, NextResponse } from 'next/server';
import { TournamentStatus } from '@prisma/client';
import db from '@/lib/db';
import { BackgroundJobSystem } from '@/lib/background-jobs';
import { TournamentService } from '@/lib/tournament-service';
import { TournamentEventEmitter } from '@/lib/tournament-webhooks';

export const dynamic = 'force-dynamic';

/**
 * Start a tournament and begin automated match execution
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { tournamentId: string } }
) {
  try {
    const { tournamentId } = params;

    // Validate tournament exists and can be started
    const tournament = await TournamentService.getTournamentById(tournamentId);
    if (!tournament) {
      return NextResponse.json(
        { error: 'Tournament not found' },
        { status: 404 }
      );
    }

    if (tournament.status !== TournamentStatus.DRAFT && tournament.status !== TournamentStatus.SCHEDULED) {
      return NextResponse.json(
        { error: `Cannot start tournament with status: ${tournament.status}` },
        { status: 400 }
      );
    }

    // Check if tournament has enough participants
    if (tournament.participants.length < 2) {
      return NextResponse.json(
        { error: 'Tournament must have at least 2 participants' },
        { status: 400 }
      );
    }

    // Check if tournament has scheduled matches
    if (tournament.matches.length === 0) {
      return NextResponse.json(
        { error: 'Tournament has no scheduled matches' },
        { status: 400 }
      );
    }

    // Update tournament status to ACTIVE
    const updatedTournament = await TournamentService.updateTournamentStatus(
      tournamentId,
      TournamentStatus.ACTIVE
    );

    // Start the background job for automated execution
    await BackgroundJobSystem.startTournamentJob(tournamentId);

    // Emit tournament started event
    TournamentEventEmitter.tournamentStarted(tournamentId, {
      name: tournament.name,
      participantCount: tournament.participants.length,
    });

    return NextResponse.json({
      message: 'Tournament started successfully',
      tournament: {
        id: updatedTournament.id,
        name: tournament.name,
        status: updatedTournament.status,
        participantCount: tournament.participants.length,
        totalMatches: tournament.matches.length,
      },
      jobStatus: BackgroundJobSystem.getJobStatus(tournamentId),
    });

  } catch (error) {
    console.error('Error starting tournament:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to start tournament',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}