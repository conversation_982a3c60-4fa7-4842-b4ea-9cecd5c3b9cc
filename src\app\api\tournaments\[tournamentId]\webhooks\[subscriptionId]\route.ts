import { NextRequest, NextResponse } from 'next/server';
import { TournamentWebhooks } from '@/lib/tournament-webhooks';

export const dynamic = 'force-dynamic';

/**
 * Get a specific webhook subscription
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { tournamentId: string; subscriptionId: string } }
) {
  try {
    const { subscriptionId } = params;

    const subscription = TournamentWebhooks.getSubscription(subscriptionId);
    if (!subscription) {
      return NextResponse.json(
        { error: 'Webhook subscription not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      subscription: {
        id: subscription.id,
        tournamentId: subscription.tournamentId,
        url: subscription.url,
        events: subscription.events,
        active: subscription.active,
        createdAt: subscription.createdAt,
      },
    });

  } catch (error) {
    console.error('Error getting webhook subscription:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to get webhook subscription',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Update a webhook subscription
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { tournamentId: string; subscriptionId: string } }
) {
  try {
    const { subscriptionId } = params;
    const body = await request.json();

    const subscription = TournamentWebhooks.getSubscription(subscriptionId);
    if (!subscription) {
      return NextResponse.json(
        { error: 'Webhook subscription not found' },
        { status: 404 }
      );
    }

    // Validate updates
    const allowedUpdates = ['active', 'events'];
    const updates: any = {};

    for (const [key, value] of Object.entries(body)) {
      if (allowedUpdates.includes(key)) {
        updates[key] = value;
      }
    }

    if (Object.keys(updates).length === 0) {
      return NextResponse.json(
        { error: 'No valid updates provided' },
        { status: 400 }
      );
    }

    // Validate event types if provided
    if (updates.events) {
      const validEvents = [
        'tournament_started', 'tournament_paused', 'tournament_resumed', 'tournament_completed',
        'match_started', 'match_completed', 'match_failed', 'round_completed'
      ];

      const invalidEvents = updates.events.filter((event: string) => !validEvents.includes(event));
      if (invalidEvents.length > 0) {
        return NextResponse.json(
          { 
            error: 'Invalid event types',
            invalidEvents,
            validEvents 
          },
          { status: 400 }
        );
      }
    }

    // Update subscription
    const success = TournamentWebhooks.updateSubscription(subscriptionId, updates);
    if (!success) {
      return NextResponse.json(
        { error: 'Failed to update subscription' },
        { status: 500 }
      );
    }

    const updatedSubscription = TournamentWebhooks.getSubscription(subscriptionId);

    return NextResponse.json({
      message: 'Webhook subscription updated successfully',
      subscription: {
        id: updatedSubscription!.id,
        tournamentId: updatedSubscription!.tournamentId,
        url: updatedSubscription!.url,
        events: updatedSubscription!.events,
        active: updatedSubscription!.active,
        createdAt: updatedSubscription!.createdAt,
      },
    });

  } catch (error) {
    console.error('Error updating webhook subscription:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to update webhook subscription',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Delete a webhook subscription
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { tournamentId: string; subscriptionId: string } }
) {
  try {
    const { subscriptionId } = params;

    const success = TournamentWebhooks.unsubscribe(subscriptionId);
    if (!success) {
      return NextResponse.json(
        { error: 'Webhook subscription not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: 'Webhook subscription deleted successfully',
      subscriptionId,
    });

  } catch (error) {
    console.error('Error deleting webhook subscription:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to delete webhook subscription',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}