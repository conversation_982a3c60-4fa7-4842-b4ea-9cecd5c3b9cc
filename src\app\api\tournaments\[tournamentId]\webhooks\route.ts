import { NextRequest, NextResponse } from 'next/server';
import { TournamentWebhooks, TournamentEvent } from '@/lib/tournament-webhooks';
import { TournamentService } from '@/lib/tournament-service';

export const dynamic = 'force-dynamic';

/**
 * Create a webhook subscription for tournament events
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { tournamentId: string } }
) {
  try {
    const { tournamentId } = params;
    const body = await request.json();

    // Validate tournament exists
    const tournament = await TournamentService.getTournamentById(tournamentId);
    if (!tournament) {
      return NextResponse.json(
        { error: 'Tournament not found' },
        { status: 404 }
      );
    }

    // Validate request body
    const { url, events } = body;
    
    if (!url || typeof url !== 'string') {
      return NextResponse.json(
        { error: 'Webhook URL is required' },
        { status: 400 }
      );
    }

    if (!events || !Array.isArray(events) || events.length === 0) {
      return NextResponse.json(
        { error: 'At least one event type must be specified' },
        { status: 400 }
      );
    }

    // Validate event types
    const validEvents: TournamentEvent['type'][] = [
      'tournament_started', 'tournament_paused', 'tournament_resumed', 'tournament_completed',
      'match_started', 'match_completed', 'match_failed', 'round_completed'
    ];

    const invalidEvents = events.filter((event: string) => !validEvents.includes(event as any));
    if (invalidEvents.length > 0) {
      return NextResponse.json(
        { 
          error: 'Invalid event types',
          invalidEvents,
          validEvents 
        },
        { status: 400 }
      );
    }

    // Create subscription
    const subscriptionId = TournamentWebhooks.subscribe({
      tournamentId,
      url,
      events,
      active: true,
    });

    return NextResponse.json({
      message: 'Webhook subscription created successfully',
      subscriptionId,
      tournamentId,
      url,
      events,
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating webhook subscription:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to create webhook subscription',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Get all webhook subscriptions for a tournament
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { tournamentId: string } }
) {
  try {
    const { tournamentId } = params;

    // Validate tournament exists
    const tournament = await TournamentService.getTournamentById(tournamentId);
    if (!tournament) {
      return NextResponse.json(
        { error: 'Tournament not found' },
        { status: 404 }
      );
    }

    // Get subscriptions
    const subscriptions = TournamentWebhooks.getSubscriptions(tournamentId);

    // Get recent events
    const recentEvents = TournamentWebhooks.getEventHistory(tournamentId, 10);

    return NextResponse.json({
      tournamentId,
      subscriptions: subscriptions.map(sub => ({
        id: sub.id,
        url: sub.url,
        events: sub.events,
        active: sub.active,
        createdAt: sub.createdAt,
      })),
      recentEvents,
      stats: TournamentWebhooks.getStats(),
    });

  } catch (error) {
    console.error('Error getting webhook subscriptions:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to get webhook subscriptions',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}