import { NextRequest, NextResponse } from 'next/server';
import { TournamentService } from '@/lib/tournament-service';
import { TournamentFormat } from '@prisma/client';

export async function GET() {
  try {
    const tournaments = await TournamentService.getAllTournaments();
    return NextResponse.json(tournaments);
  } catch (error) {
    console.error('Error fetching tournaments:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tournaments' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, format, participantIds } = body;

    if (!name || !format || !participantIds || !Array.isArray(participantIds)) {
      return NextResponse.json(
        { error: 'Name, format, and participantIds are required' },
        { status: 400 }
      );
    }

    if (!Object.values(TournamentFormat).includes(format)) {
      return NextResponse.json(
        { error: 'Invalid tournament format' },
        { status: 400 }
      );
    }

    if (participantIds.length < 2) {
      return NextResponse.json(
        { error: 'At least 2 participants are required' },
        { status: 400 }
      );
    }

    const tournament = await TournamentService.createTournament({
      name,
      format,
      participantIds,
    });

    return NextResponse.json(tournament, { status: 201 });
  } catch (error) {
    console.error('Error creating tournament:', error);
    return NextResponse.json(
      { error: 'Failed to create tournament' },
      { status: 500 }
    );
  }
}
