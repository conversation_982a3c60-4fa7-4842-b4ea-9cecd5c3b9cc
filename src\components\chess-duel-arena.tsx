"use client";

import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import type { Chess as ChessInstance, Piece, Square } from 'chess.js';
import { Chess } from 'chess.js';
import { Chessboard } from 'react-chessboard';
import type { GenerateMoveOutput } from '@/ai/flows/generate-chess-move';
import { generateMove } from '@/ai/flows/generate-chess-move';
import { getGames, saveGame, deleteGame } from '@/app/actions/history';
import { GameActivitySidebar } from './game-activity-sidebar';
import { LiveGamesManager } from './live-games-manager';
import { GameHistory } from './game-history';
import { ELODisplay, ELOBadge } from './elo-display';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from './ui/accordion';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { ScrollArea } from './ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { Bot, CheckCircle, Copy, FileText, Loader2, Play, RotateCcw, User, Users, XCircle, Square as StopIcon, PlayCircle, History as HistoryIcon, RefreshCw, Wand2, Eye, Trash2, ChevronLeft, ChevronRight, Share2, Download, AlertCircle, Pause, Clock, Trophy } from 'lucide-react';
import { Badge } from './ui/badge';

type Player = 'white' | 'black';
type GameMode = 'ai-vs-ai' | 'human-vs-ai' | 'freestyle-ai-vs-ai';
type GameState = 'idle' | 'starting' | 'running' | 'paused' | 'processing-move' | 'completed' | 'error';
type Model = 
  | 'gemini-2.0-flash' 
  | 'gemini-2.5-flash' 
  | 'gemini-2.5-pro' 
  | 'openrouter/openai/gpt-oss-20b:free'
  | 'openrouter/z-ai/glm-4.5-air:free'
  | 'openrouter/moonshotai/kimi-k2:free'
  | 'openrouter/google/gemma-3n-e2b-it:free'
  | 'openrouter/deepseek/deepseek-r1-0528:free'
  | 'openrouter/qwen/qwen3-235b-a22b:free';

const ALL_MODELS: Model[] = [
  // Google AI (Direct - Genkit only)
  'gemini-2.0-flash', 
  'gemini-2.5-flash', 
  'gemini-2.5-pro', 
  
  // OpenRouter - Free Models
  'openrouter/openai/gpt-oss-20b:free',
  'openrouter/z-ai/glm-4.5-air:free',
  'openrouter/moonshotai/kimi-k2:free',
  'openrouter/google/gemma-3n-e2b-it:free',
  'openrouter/deepseek/deepseek-r1-0528:free',
  'openrouter/qwen/qwen3-235b-a22b:free'
];

// Helper function to display simplified model names in the UI
const getModelDisplayName = (model: string): string => {
  if (!model.startsWith('openrouter/')) return model;
  
  const modelMap: Record<string, string> = {
    'openrouter/openai/gpt-oss-20b:free': 'GPT OSS 20B',
    'openrouter/z-ai/glm-4.5-air:free': 'GLM 4.5 Air',
    'openrouter/moonshotai/kimi-k2:free': 'Kimi K2',
    'openrouter/google/gemma-3n-e2b-it:free': 'Gemma 3N',
    'openrouter/deepseek/deepseek-r1-0528:free': 'DeepSeek R1',
    'openrouter/qwen/qwen3-235b-a22b:free': 'Qwen3 235B'
  };
  
  return modelMap[model] || model.replace('openrouter/', '');
};

// Helper function to get game state display information
const getGameStateDisplay = (state: GameState): { icon: React.ReactNode; text: string; variant: 'default' | 'secondary' | 'destructive' | 'outline' } => {
  switch (state) {
    case 'idle':
      return { icon: <StopIcon className="h-3 w-3" />, text: 'Ready', variant: 'outline' };
    case 'starting':
      return { icon: <Loader2 className="h-3 w-3 animate-spin" />, text: 'Starting...', variant: 'default' };
    case 'running':
      return { icon: <Play className="h-3 w-3" />, text: 'Running', variant: 'default' };
    case 'paused':
      return { icon: <Pause className="h-3 w-3" />, text: 'Paused', variant: 'secondary' };
    case 'processing-move':
      return { icon: <Clock className="h-3 w-3 animate-pulse" />, text: 'Processing Move', variant: 'default' };
    case 'completed':
      return { icon: <CheckCircle className="h-3 w-3" />, text: 'Completed', variant: 'secondary' };
    case 'error':
      return { icon: <AlertCircle className="h-3 w-3" />, text: 'Error', variant: 'destructive' };
    default:
      return { icon: <StopIcon className="h-3 w-3" />, text: 'Unknown', variant: 'outline' };
  }
};

type HistoryItem = {
  whiteMove: string;
  whiteReason?: string;
  whiteAnalysis?: GenerateMoveOutput['analysis'];
  whiteOpponentPrediction?: string;
  blackMove?: string;
  blackReason?: string;
  blackAnalysis?: GenerateMoveOutput['analysis'];
  blackOpponentPrediction?: string;
  moveNumber: number;
};

type GameHistoryEntry = {
    id: string;
    white: string;
    black: string;
    result: string | null;
    pgn: string;
    status: 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
    createdAt: string;
}

const Typewriter = ({ text }: { text: string }) => {
    const [displayedText, setDisplayedText] = useState('');
    useEffect(() => {
        setDisplayedText(''); // Reset when text changes
        if (text) {
            let i = 0;
            const intervalId = setInterval(() => {
                setDisplayedText(text.slice(0, i + 1));
                i++;
                if (i >= text.length) {
                    clearInterval(intervalId);
                }
            }, 10); // Adjust typing speed here
            return () => clearInterval(intervalId);
        }
    }, [text]);

    return <>{displayedText}</>;
};

const generateChess960Fen = (): string => {
    const backRank: (string | null)[] = Array(8).fill(null);
    
    // Place bishops on opposite colored squares (Chess960 requirement)
    const lightSquares = [0, 2, 4, 6];
    const darkSquares = [1, 3, 5, 7];
    const bishop1Pos = lightSquares.splice(Math.floor(Math.random() * lightSquares.length), 1)[0];
    const bishop2Pos = darkSquares.splice(Math.floor(Math.random() * darkSquares.length), 1)[0];
    backRank[bishop1Pos] = 'B';
    backRank[bishop2Pos] = 'B';
    
    // Place queen randomly among remaining squares
    let emptySquares = backRank.map((p, i) => p === null ? i : -1).filter(i => i !== -1);
    const queenPos = emptySquares.splice(Math.floor(Math.random() * emptySquares.length), 1)[0];
    backRank[queenPos] = 'Q';
    
    // Place knights randomly
    const knight1Pos = emptySquares.splice(Math.floor(Math.random() * emptySquares.length), 1)[0];
    backRank[knight1Pos] = 'N';
    const knight2Pos = emptySquares.splice(Math.floor(Math.random() * emptySquares.length), 1)[0];
    backRank[knight2Pos] = 'N';
    
    // Place rooks with king between them (Chess960 requirement for castling)
    const finalEmptySquares = backRank.map((p, i) => p === null ? i : -1).filter(i => i !== -1);
    finalEmptySquares.sort((a, b) => a - b); // Sort to ensure correct rook-king-rook order
    backRank[finalEmptySquares[0]] = 'R';
    backRank[finalEmptySquares[1]] = 'K';
    backRank[finalEmptySquares[2]] = 'R';
    
    const whiteRank = backRank.join('');
    const blackRank = whiteRank.toLowerCase();
    
    // Create Chess960 position with proper castling rights
    // In Chess960, castling rights depend on rook positions
    const kingPos = finalEmptySquares[1];
    const leftRookPos = finalEmptySquares[0];
    const rightRookPos = finalEmptySquares[2];
    
    // Set up castling availability based on Chess960 rules
    let castlingRights = '';
    
    // White castling rights
    if (rightRookPos === 7) castlingRights += 'K'; // Kingside castling if rook is on h-file
    if (leftRookPos === 0) castlingRights += 'Q';  // Queenside castling if rook is on a-file
    
    // Black castling rights (mirror of white)
    if (rightRookPos === 7) castlingRights += 'k';
    if (leftRookPos === 0) castlingRights += 'q';
    
    // If no standard castling positions, still allow Chess960 castling
    if (castlingRights === '') {
        // In Chess960, castling is always possible initially unless explicitly disabled
        castlingRights = 'KQkq';
    }
    
    const fen = `${blackRank}/pppppppp/8/8/8/8/PPPPPPPP/${whiteRank} w ${castlingRights} - 0 1`;
    
    try {
        const gameForFen = new Chess(fen);
        return gameForFen.fen();
    } catch (error) {
        console.warn('Chess960 FEN generation failed, falling back to standard position:', error);
        return 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1';
    }
};

export default function ChessDuelArena() {
  const gameRef = useRef<ChessInstance | null>(null);
  const eventSourceRef = useRef<EventSource | null>(null);
  const [fen, setFen] = useState('start');
  const [pgn, setPgn] = useState('');
  const [gameMode, setGameMode] = useState<GameMode>('ai-vs-ai');
  const [humanPlayer, setHumanPlayer] = useState<Player>('white');
  const [whiteModel, setWhiteModel] = useState<Model>('gemini-2.0-flash');
  const [blackModel, setBlackModel] = useState<Model>('gemini-2.5-pro');
  const [isGameRunning, setIsGameRunning] = useState(false);
  const [isGameStarted, setIsGameStarted] = useState(false);
  const [gameStatus, setGameStatus] = useState('Game has not started.');
  const [currentGameState, setCurrentGameState] = useState<GameState>('idle');
  const [history, setHistory] = useState<HistoryItem[]>([]);
  const [logs, setLogs] = useState<string[]>(['Welcome to Chess Duel Arena!']);
  const [isThinking, setIsThinking] = useState(false);
  const [lastMove, setLastMove] = useState<{ from: string, to: string } | null>(null);
  const [moveFrom, setMoveFrom] = useState<Square | null>(null);
  const [optionSquares, setOptionSquares] = useState({});
  const [gameHistory, setGameHistory] = useState<GameHistoryEntry[]>([]);
  const [isHistoryLoading, setIsHistoryLoading] = useState(false);
  const [isAutorunning, setIsAutorunning] = useState(false);
  const [activeAccordionItem, setActiveAccordionItem] = useState<string | null>(null);
  const [currentMoveNumber, setCurrentMoveNumber] = useState(0);
  const [spectatedGameId, setSpectatedGameId] = useState<string | null>(null);
  const [isReconnecting, setIsReconnecting] = useState(false);
  const reconnectTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const maxReconnectAttempts = 3;
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const [whiteEloRating, setWhiteEloRating] = useState<number | null>(null);
  const [blackEloRating, setBlackEloRating] = useState<number | null>(null);
  const [whiteProfile, setWhiteProfile] = useState<any>(null);
  const [blackProfile, setBlackProfile] = useState<any>(null);
  const [whiteEloChange, setWhiteEloChange] = useState<number | null>(null);
  const [blackEloChange, setBlackEloChange] = useState<number | null>(null);
  const [showLiveGames, setShowLiveGames] = useState(false);
  const [showGameHistory, setShowGameHistory] = useState(false);
  const [lastHeartbeat, setLastHeartbeat] = useState<number | null>(null);
  const [connectionHealth, setConnectionHealth] = useState<'good' | 'poor' | 'bad'>('good');
  const moveRefs = useRef<(HTMLDivElement | null)[]>([]);





  // Function to fetch ELO ratings for current players
  const fetchEloRatings = useCallback(async () => {
    try {
      const profilesResponse = await fetch('/api/profiles');
      if (!profilesResponse.ok) return;

      const profiles = await profilesResponse.json();

      // Get white player name
      const whitePlayerName = gameMode === 'human-vs-ai' && humanPlayer === 'white' ? 'Human' : whiteModel;
      const blackPlayerName = gameMode === 'human-vs-ai' && humanPlayer === 'black' ? 'Human' : blackModel;

      const whiteProfile = profiles.find((p: any) => p.name === whitePlayerName);
      const blackProfile = profiles.find((p: any) => p.name === blackPlayerName);

      setWhiteEloRating(whiteProfile?.eloRating || null);
      setBlackEloRating(blackProfile?.eloRating || null);
    } catch (error) {
      console.error('Error fetching ELO ratings:', error);
    }
  }, [whiteModel, blackModel, gameMode, humanPlayer]);

  // Fetch ELO ratings when models change
  useEffect(() => {
    fetchEloRatings();
  }, [fetchEloRatings]);

  // Helper functions for localStorage persistence
  const saveSpectatedGame = useCallback((gameId: string | null) => {
    if (typeof window !== 'undefined') {
      if (gameId) {
        localStorage.setItem('spectatedGameId', gameId);
      } else {
        localStorage.removeItem('spectatedGameId');
      }
    }
  }, []);

  const getSpectatedGame = useCallback((): string | null => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('spectatedGameId');
    }
    return null;
  }, []);

  const game = useMemo(() => {
    if (typeof window === 'undefined') return null;
    if (gameRef.current === null) gameRef.current = new Chess();
    return gameRef.current;
  }, []);

  // Initialize toast and addLog
  const { toast } = useToast();
  const addLog = useCallback((message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev]);
  }, []);

  // Function to add game activity to sidebar
  const addGameActivity = useCallback((activity: any) => {
    if (typeof window !== 'undefined' && (window as any).addGameActivity) {
      (window as any).addGameActivity(activity);
    }
  }, []);

  // Fetch profile data for ELO information
  const fetchProfileData = useCallback(async (modelName: string): Promise<any> => {
    try {
      const response = await fetch('/api/profiles');
      if (response.ok) {
        const profiles = await response.json();
        const profile = profiles.find((p: any) => p.name === modelName || p.model === modelName);
        return profile || null;
      }
    } catch (error) {
      console.error('Error fetching profile data:', error);
    }
    return null;
  }, []);

  // Update ELO data when models change
  useEffect(() => {
    const updateELOData = async () => {
      if (gameMode === 'ai-vs-ai' || gameMode === 'freestyle-ai-vs-ai') {
        const whiteProfileData = await fetchProfileData(whiteModel);
        const blackProfileData = await fetchProfileData(blackModel);
        
        setWhiteProfile(whiteProfileData);
        setBlackProfile(blackProfileData);
        setWhiteEloRating(whiteProfileData?.eloRating || 1500);
        setBlackEloRating(blackProfileData?.eloRating || 1500);
      } else {
        // Reset for human vs AI games
        setWhiteProfile(null);
        setBlackProfile(null);
        setWhiteEloRating(null);
        setBlackEloRating(null);
      }
    };

    updateELOData();
  }, [whiteModel, blackModel, gameMode, fetchProfileData]);

  // Function to update ELO ratings with logging
  const updateEloRatingsWithLog = useCallback(async (whitePlayer: string, blackPlayer: string, result: '1-0' | '0-1' | '1/2-1/2', logFn: (msg: string) => void) => {
    try {
      // Check if both players are LLM profiles by trying to find them
      const profilesResponse = await fetch('/api/profiles');
      if (!profilesResponse.ok) return;

      const profiles = await profilesResponse.json();
      const whiteProfile = profiles.find((p: any) => p.name === whitePlayer);
      const blackProfile = profiles.find((p: any) => p.name === blackPlayer);

      if (!whiteProfile || !blackProfile) {
        logFn(`ELO_UPDATE: Skipping ELO update - not all players are LLM profiles`);
        return;
      }

      // Convert chess result to ELO result format
      let eloResult: 'white' | 'black' | 'draw';
      if (result === '1-0') {
        eloResult = 'white';
      } else if (result === '0-1') {
        eloResult = 'black';
      } else {
        eloResult = 'draw';
      }

      // Update ELO ratings via API
      const eloResponse = await fetch('/api/elo/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          whiteProfileId: whiteProfile.id,
          blackProfileId: blackProfile.id,
          result: eloResult,
        }),
      });

      if (eloResponse.ok) {
        const eloData = await eloResponse.json();
        logFn(`ELO_UPDATE: White ${whiteProfile.eloRating} → ${eloData.whiteProfile.newElo} (${eloData.whiteProfile.eloChange >= 0 ? '+' : ''}${eloData.whiteProfile.eloChange})`);
        logFn(`ELO_UPDATE: Black ${blackProfile.eloRating} → ${eloData.blackProfile.newElo} (${eloData.blackProfile.eloChange >= 0 ? '+' : ''}${eloData.blackProfile.eloChange})`);

        // Update local state with ELO changes
        setWhiteEloChange(eloData.whiteProfile.eloChange);
        setBlackEloChange(eloData.blackProfile.eloChange);
        setWhiteEloRating(eloData.whiteProfile.newElo);
        setBlackEloRating(eloData.blackProfile.newElo);

        // Add ELO update activity to sidebar
        addGameActivity({
          type: 'elo_update',
          whitePlayer,
          blackPlayer,
          whiteElo: eloData.whiteProfile.newElo,
          blackElo: eloData.blackProfile.newElo,
          whiteEloChange: eloData.whiteProfile.eloChange,
          blackEloChange: eloData.blackProfile.eloChange
        });

        toast({
          title: "ELO Ratings Updated",
          description: `White: ${eloData.whiteEloChange >= 0 ? '+' : ''}${eloData.whiteEloChange}, Black: ${eloData.blackEloChange >= 0 ? '+' : ''}${eloData.blackEloChange}`,
          duration: 5000,
        });
      } else {
        logFn(`ELO_UPDATE_ERROR: Failed to update ELO ratings - ${eloResponse.status}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      logFn(`ELO_UPDATE_ERROR: ${errorMessage}`);
    }
  }, [toast]);

  const updateBoardState = useCallback((theGame?: ChessInstance) => {
    const g = theGame || game;
    if (!g) return;
    setFen(g.fen());
    setPgn(g.pgn({ maxWidth: 5, newline: ' ' }));
  }, [game]);

  const setPgnHeaders = useCallback(() => {
    if (!game) return;
    const whitePlayer = gameMode === 'human-vs-ai' && humanPlayer === 'white' ? 'Human' : whiteModel;
    const blackPlayer = gameMode === 'human-vs-ai' && humanPlayer === 'black' ? 'Human' : blackModel;
    let event = `AI vs AI: ${whiteModel} vs ${blackModel}`;
    if (gameMode === 'human-vs-ai') event = 'Human vs AI Challenge';
    if (gameMode === 'freestyle-ai-vs-ai') event = 'Freestyle AI vs AI (Chess960)';
    game.header('Event', event);
    game.header('Site', 'Chess Duel Arena');
    game.header('Date', new Date().toISOString().split('T')[0]);
    game.header('Round', '1');
    game.header('White', whitePlayer);
    game.header('Black', blackPlayer);
    game.header('Result', '*');
  }, [game, gameMode, humanPlayer, whiteModel, blackModel]);

  const fetchGameHistory = useCallback(async () => {
    setIsHistoryLoading(true);
    addLog('Fetching game history...');
    try {
        const games = await getGames();
        setGameHistory(games);
        addLog(`Successfully fetched ${games.length} games from history.`);
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Unknown error";
        addLog(`ERROR_FETCHING_HISTORY: ${errorMessage}`);
        toast({ variant: 'destructive', title: "Failed to Fetch History", description: "Could not retrieve game history. See logs." });
    } finally {
        setIsHistoryLoading(false);
    }
  }, [addLog, toast]);

  const handleEndGame = useCallback(async (status: string, result?: '1-0' | '0-1' | '1/2-1/2') => {
      addLog(`GAME_END: ${status}`);
      setGameStatus(status);
      setIsGameRunning(false);
      setCurrentGameState('completed');
      if (game && result) {
        game.header('Result', result);
        updateBoardState();
        try {
            const whitePlayer = game.header().White || 'Unknown';
            const blackPlayer = game.header().Black || 'Unknown';
            const finalPgn = game.pgn();

            // Add game end activity to sidebar
            addGameActivity({
              type: 'game_end',
              whitePlayer,
              blackPlayer,
              result,
              gameId: Date.now().toString()
            });

            // Save game to history
            await saveGame({
                white: whitePlayer,
                black: blackPlayer,
                result: result,
                pgn: finalPgn
            });

            // Update ELO ratings if both players are LLM profiles
            try {
                await updateEloRatingsWithLog(whitePlayer, blackPlayer, result, addLog);
            } catch (eloError) {
                const eloErrorMessage = eloError instanceof Error ? eloError.message : "Unknown ELO error";
                addLog(`ELO_UPDATE_ERROR: ${eloErrorMessage}`);
                // Don't show error toast for ELO updates as it's not critical for game completion
            }

            addLog(`Game saved to history.`);
            toast({ title: "Game Saved", description: "The game has been saved to the history." });
            // Only fetch history if we're not currently spectating a live game
            if (!spectatedGameId) {
                fetchGameHistory();
            }
        } catch(error) {
            const errorMessage = error instanceof Error ? error.message : "Unknown error";
            addLog(`ERROR_SAVING_GAME: ${errorMessage}`);
            toast({ variant: 'destructive', title: "Failed to Save Game", description: "Could not save game to history. See logs." });
        }
      }
  }, [addLog, game, updateBoardState, toast, spectatedGameId]);

  const updateGameStatus = useCallback((g: ChessInstance | null) => {
    if (!g) return;
    let status = '';
    const turn = g.turn() === 'b' ? 'Black' : 'White';
    if (g.isCheckmate()) {
      const winner = turn === 'Black' ? 'White' : 'Black';
      status = `Checkmate! ${winner} wins.`;
      handleEndGame(status, winner === 'White' ? '1-0' : '0-1');
    } else if (g.isDraw() || g.isStalemate() || g.isThreefoldRepetition() || g.isInsufficientMaterial()) {
      status = 'Draw!';
      handleEndGame(status, '1/2-1/2');
    } else {
      const player = g.turn() === 'w' ? 'White' : 'Black';
      const isHumanTurn = gameMode === 'human-vs-ai' && g.turn() === humanPlayer[0];
      
      if (spectatedGameId) {
        status = `🔴 LIVE: Watching ${player}'s turn in ongoing game`;
      } else if (isHumanTurn) {
        status = `🎯 Your turn to move as ${player}`;
      } else if (isGameRunning) {
        status = `⚡ Game Running - ${player}'s turn (AI thinking...)`;
      } else if (!isGameRunning && isGameStarted) {
        status = `⏸️ Game Paused - ${player}'s turn (click Resume to continue)`;
      } else {
        status = `${player}'s turn to move`;
      }
      setGameStatus(status);
    }
  }, [handleEndGame, gameMode, humanPlayer, isGameRunning, isGameStarted, spectatedGameId]);
  
  const resetGame = useCallback(() => {
    if (eventSourceRef.current) {
        eventSourceRef.current.close();
        eventSourceRef.current = null;
    }
    if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
    }
    setIsReconnecting(false);
    setReconnectAttempts(0);
    if (!game) return;
    const isFreestyle = gameMode === 'freestyle-ai-vs-ai';
    game.clear();
    if (isFreestyle) {
        const randomFen = generateChess960Fen();
        game.load(randomFen);
        // Set Chess960 variant and starting FEN in headers
        game.header('Variant', 'Chess960');
        game.header('FEN', randomFen);
        game.header('SetUp', '1'); // Indicates non-standard starting position
        addLog(`FREESTYLE_MODE (Chess960): Loaded random position: ${randomFen}`);
    } else {
        game.reset();
    }
    setPgnHeaders();
    updateBoardState();
    setIsGameRunning(false);
    setIsGameStarted(false);
    setCurrentGameState('idle');
    setHistory([]);
    setLastMove(null);
    setMoveFrom(null);
    setOptionSquares({});
    setLogs(['Game reset. Select mode, models and start.']);
    setGameStatus('Game reset. Select mode, models and start.');
    setActiveAccordionItem(null);
    setCurrentMoveNumber(0);
    setSpectatedGameId(null);
    saveSpectatedGame(null);
  }, [game, updateBoardState, addLog, gameMode, setPgnHeaders]);

  const loadPgnIntoGame = useCallback((pgnToLoad: string) => {
    if (!game) return;
    game.loadPgn(pgnToLoad);
    const newHistory: HistoryItem[] = [];
    const moves = game.history({ verbose: true });
    moves.forEach((move: any, index: number) => {
      if (index % 2 === 0) {
        newHistory.push({
          moveNumber: Math.floor(index / 2) + 1,
          whiteMove: move.san,
          whiteReason: 'Move from history.',
        });
      } else {
        const lastItem = newHistory[newHistory.length - 1];
        if (lastItem) {
          lastItem.blackMove = move.san;
          lastItem.blackReason = 'Move from history.';
        }
      }
    });
    setHistory(newHistory);
    updateBoardState(); // Update FEN and PGN state
    navigateToMove(moves.length);
  }, [game, updateBoardState]);

  const processMove = (move: any, moveReasoning?: GenerateMoveOutput) => {
    if (!game) return false;
    const madeMove = game.move(move);
    if (madeMove) {
        addLog(`MOVE_SUCCESS: ${madeMove.san}.`);
        updateBoardState();
        updateGameStatus(game);
        setLastMove({ from: madeMove.from, to: madeMove.to });
        setCurrentMoveNumber(game.history().length);
        const currentPlayer = madeMove.color === 'w' ? 'white' : 'black';

        // Add move activity to sidebar
        const currentPlayerName = gameMode === 'human-vs-ai' &&
          ((madeMove.color === 'w' && humanPlayer === 'white') ||
           (madeMove.color === 'b' && humanPlayer === 'black')) ? 'Human' :
          (madeMove.color === 'w' ? whiteModel : blackModel);

        addGameActivity({
          type: 'move',
          whitePlayer: gameMode === 'human-vs-ai' && humanPlayer === 'white' ? 'Human' : whiteModel,
          blackPlayer: gameMode === 'human-vs-ai' && humanPlayer === 'black' ? 'Human' : blackModel,
          move: madeMove.san,
          gameId: Date.now().toString()
        });

        // Add reasoning activity if available
        if (moveReasoning?.reasoning) {
            addGameActivity({
              type: 'reasoning',
              whitePlayer: gameMode === 'human-vs-ai' && humanPlayer === 'white' ? 'Human' : whiteModel,
              blackPlayer: gameMode === 'human-vs-ai' && humanPlayer === 'black' ? 'Human' : blackModel,
              reasoning: moveReasoning.reasoning,
              move: madeMove.san,
              gameId: Date.now().toString()
            });
        }
        setHistory(h => {
            const newHistory = [...h];
            if (currentPlayer === 'white') {
                return [...newHistory, {
                    moveNumber: game.moveNumber(),
                    whiteMove: madeMove.san,
                    whiteReason: moveReasoning?.reason ?? "Human move",
                    whiteAnalysis: moveReasoning?.analysis,
                    whiteOpponentPrediction: moveReasoning?.opponentPrediction,
                }];
            } else {
                const lastItemIndex = newHistory.length - 1;
                if (lastItemIndex >= 0) {
                     newHistory[lastItemIndex] = {
                        ...newHistory[lastItemIndex],
                        blackMove: madeMove.san,
                        blackReason: moveReasoning?.reason ?? "Human move",
                        blackAnalysis: moveReasoning?.analysis,
                        blackOpponentPrediction: moveReasoning?.opponentPrediction,
                    };
                    return newHistory;
                }
            }
            return newHistory;
        });
        const accordionId = `item-${history.length - (currentPlayer === 'black' ? 1 : 0)}`;
        setActiveAccordionItem(accordionId);
        setTimeout(() => {
          const lastMoveIndex = history.length - (currentPlayer === 'black' ? 1 : 0);
          moveRefs.current[lastMoveIndex]?.scrollIntoView({ behavior: 'smooth', block: 'end' });
        }, 100);
        return true;
    }
    return false;
  };

  const attemptMove = (sourceSquare: Square, targetSquare: Square) => {
    if (!game) return false;
    const move = { from: sourceSquare, to: targetSquare, promotion: 'q' };
    if (processMove(move)) {
        setMoveFrom(null);
        setOptionSquares({});
        return true;
    }
    setMoveFrom(null);
    setOptionSquares({});
    toast({
      variant: "destructive",
      title: "Invalid Move",
      description: `The move from ${sourceSquare} to ${targetSquare} is not allowed.`,
    })
    return false;
  }

  const onPieceDrop = (sourceSquare: Square, targetSquare: Square) => {
    if (!game) return false;
    const isHumanTurn = isGameRunning && gameMode === 'human-vs-ai' && game.turn() === humanPlayer[0];
    if (!isHumanTurn) return false;
    return attemptMove(sourceSquare, targetSquare);
  };

  const onSquareClick = (square: Square) => {
    if (!game) return;
    
    // Allow history navigation - only restrict actual moves when viewing history
    if (currentMoveNumber !== game.history().length && gameMode === 'human-vs-ai') {
        toast({ title: "Viewing History", description: "Use Next/Prev buttons to navigate moves. Click 'Go to Latest' to resume playing."});
        return;
    }
    
    const isHumanTurn = isGameRunning && gameMode === 'human-vs-ai' && game.turn() === humanPlayer[0] && currentMoveNumber === game.history().length;
    if (!isHumanTurn && gameMode === 'human-vs-ai') {
        setMoveFrom(null);
        setOptionSquares({});
        return;
    }

    if (!moveFrom) {
      const moves = game.moves({ square, verbose: true });
      if (moves.length > 0) {
        setMoveFrom(square);
        const newOptions = moves.reduce((acc: any, move: any) => {
          acc[move.to] = { background: 'rgba(255, 255, 0, 0.4)', borderRadius: '50%' };
          return acc;
        }, {});
        setOptionSquares(newOptions);
      }
    } else {
      if (square === moveFrom) {
         setMoveFrom(null);
         setOptionSquares({});
         return;
      }
      attemptMove(moveFrom, square);
    }
  };

  useEffect(() => {
    if (!game) return;
    let timeoutId: NodeJS.Timeout;
    const makeAiMove = async () => {
      const isAITurn = isGameRunning && !game.isGameOver() &&
          (gameMode === 'ai-vs-ai' || gameMode === 'freestyle-ai-vs-ai' || (gameMode === 'human-vs-ai' && game.turn() !== humanPlayer[0]));
      if (!isAITurn || currentMoveNumber !== game.history().length) {
        setIsThinking(false);
        return;
      }
      const currentPlayer: Player = game.turn() === 'w' ? 'white' : 'black';
      const currentModel = currentPlayer === 'white' ? whiteModel : blackModel;
      setIsThinking(true);
      setCurrentGameState('processing-move');
      addLog(`Thinking... (${currentPlayer} as ${currentModel})`);
      try {
        let moveSuccessful = false;
        let attempts = 0;
        const maxAttempts = 5;
        while (attempts < maxAttempts && !moveSuccessful) {
          attempts++;
          addLog(`Attempt ${attempts}/${maxAttempts} for ${currentPlayer}...`);
          try {
            const legalMoves = game.moves({verbose: false});
            const result = await generateMove({
              fen: game.fen(),
              pgn: game.pgn(),
              player: currentPlayer,
              reasoningMode: true,
              model: currentModel,
              legalMoves: legalMoves,
              isChess960: gameMode === 'freestyle-ai-vs-ai'
            });
            if (!result || !result.move) {
              addLog(`INVALID_RESPONSE: AI (${currentModel}) returned an invalid response. Retrying...`);
              await new Promise(resolve => setTimeout(resolve, 1000));
              continue;
            }
            addLog(`AI (${currentModel}) proposed move: ${result.move}. Reason: ${result.reason}`);
            if (result.analysis && result.analysis.length > 0) {
              addLog(`AI provided detailed analysis with ${result.analysis.length} alternative moves`);
            }
            if (result.opponentPrediction) {
              addLog(`AI predicted opponent response: ${result.opponentPrediction}`);
            }
            if (processMove(result.move, result)) {
              moveSuccessful = true;
              setCurrentGameState('running');
            } else {
              addLog(`ILLEGAL_MOVE: Invalid move from ${currentModel}: ${result.move}. Retrying...`);
              await new Promise(resolve => setTimeout(resolve, 1000));
            }
          } catch (error) {
            addLog(`ERROR attempt ${attempts}/${maxAttempts}: ${error instanceof Error ? error.message : String(error)}`);
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }
        if (!moveSuccessful) {
          const winner = currentPlayer === 'white' ? 'Black' : 'White';
          const message = `Model ${currentModel} failed to provide a valid move. ${winner} wins.`;
          setCurrentGameState('error');
          handleEndGame(message, winner === 'White' ? '1-0' : '0-1');
        }
      } catch (error) {
        addLog(`FATAL_ERROR: ${error instanceof Error ? error.message : String(error)}`);
        setIsGameRunning(false);
        setCurrentGameState('error');
      } finally {
        setIsThinking(false);
      }
    };
    if (isGameRunning && game && !game.isGameOver()) {
       const isAITurn = gameMode === 'ai-vs-ai' || gameMode === 'freestyle-ai-vs-ai' || (gameMode === 'human-vs-ai' && game.turn() !== humanPlayer[0]);
       if (isAITurn) timeoutId = setTimeout(makeAiMove, 1200);
    } else if (game && game.isGameOver()) {
      setIsGameRunning(false);
    }
    return () => clearTimeout(timeoutId);
  }, [isGameRunning, game, pgn, whiteModel, blackModel, toast, updateGameStatus, addLog, handleEndGame, gameMode, humanPlayer, currentMoveNumber]);
  
  const handleStartStop = () => {
    if (isGameRunning) {
        setIsGameRunning(false);
        setCurrentGameState('paused');
        addLog('Game paused.');
        updateGameStatus(game);
    } else {
        if (!isGameStarted) {
            setCurrentGameState('starting');
            resetGame();
            setIsGameStarted(true);
            let startMessage = "Game started. White&apos;s turn.";
             if (gameMode === 'human-vs-ai') startMessage = `Game started. You are ${humanPlayer}. ${game?.turn() === humanPlayer[0] ? "It&apos;s your turn." : "AI&apos;s turn."}`
             else if (gameMode === 'freestyle-ai-vs-ai') startMessage = "Freestyle game (Chess960) started! White&apos;s turn."
            addLog(startMessage);

            // Add game start activity to sidebar
            addGameActivity({
              type: 'game_start',
              whitePlayer: gameMode === 'human-vs-ai' && humanPlayer === 'white' ? 'Human' : whiteModel,
              blackPlayer: gameMode === 'human-vs-ai' && humanPlayer === 'black' ? 'Human' : blackModel,
              whiteElo: whiteEloRating,
              blackElo: blackEloRating,
              gameId: Date.now().toString()
            });

            // Auto start background game when starting main game (for ai-vs-ai and human-vs-ai modes)
            if (gameMode === 'ai-vs-ai' || gameMode === 'human-vs-ai') {
                handleAutorun();
            }
            setCurrentGameState('running');
        } else {
            addLog('Game resumed.');
            setCurrentGameState('running');
        }
        setIsGameRunning(true);
        updateGameStatus(game);
    }
  };




  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({ title: "Copied to clipboard!" });
  };

  const handleAutorun = async () => {
    addLog('Starting background autorun...');
    setIsAutorunning(true);
    toast({ title: "Autorun Started", description: "A new game is being simulated in the background." });
    try {
      const response = await fetch('/api/autorun', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ whiteModel, blackModel }),
      });
      if (!response.ok) throw new Error((await response.json()).message || 'Failed to start autorun.');
      const newGame = await response.json();
      addLog(`AUTORUN_API: Request accepted. Game ${newGame.id} is running.`);
      
      // Add new game to history without full refresh
      setGameHistory(prev => {
        // Check if game already exists to avoid duplicates
        if (prev.some(g => g.id === newGame.id)) {
          return prev;
        }
        return [newGame, ...prev];
      });
      
      // Show status that game is starting
      toast({ 
        title: "Live Game Created", 
        description: `Game ${newGame.id} is now running. Click to spectate.`,
        duration: 5000
      });
      
      handleSpectateGame(newGame);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      addLog(`ERROR_AUTORUN: ${errorMessage}`);
      toast({ variant: 'destructive', title: "Autorun Failed", description: "Could not start the background game." });
      setCurrentGameState('error');
    } finally {
      setIsAutorunning(false);
    }
  };

  const navigateToMove = (moveNumber: number) => {
    if (!game) return;
    const tempGame = new Chess();
    tempGame.loadPgn(game.pgn());
    const history = tempGame.history({ verbose: true });
    if (moveNumber < 0 || moveNumber > history.length) return;
    
    // Create target game with proper starting position
    const targetGame = new Chess();
    const headers = game.header();
    Object.keys(headers).forEach(key => {
      const value = headers[key];
      if (value !== null) {
        targetGame.header(key, value);
      }
    });
    
    // Handle Chess960 or custom starting positions
    const variant = game.header().Variant;
    const fen = game.header().FEN;
    if (variant === 'Chess960' && fen) {
        targetGame.load(fen);
    } else if (fen) {
        targetGame.load(fen);
    }
    
    // Apply moves up to the target move number
    history.slice(0, moveNumber).forEach(move => targetGame.move(move.san));
    updateBoardState(targetGame);
    setCurrentMoveNumber(moveNumber);
    setLastMove(moveNumber > 0 ? { from: history[moveNumber - 1].from, to: history[moveNumber - 1].to } : null);
    setGameStatus(`Viewing move ${moveNumber} of ${history.length}.`);
  };

  const handlePreviousMove = () => navigateToMove(currentMoveNumber - 1);
  const handleNextMove = () => navigateToMove(currentMoveNumber + 1);
  const handleGoToLatest = () => {
    if (!game) return;
    navigateToMove(game.history().length);
    updateGameStatus(game);
  }

  const handleViewGame = (gameData: GameHistoryEntry) => {
    if (!game) return;
    resetGame();
    setTimeout(() => {
        loadPgnIntoGame(gameData.pgn);
        setIsGameStarted(true);
        setIsGameRunning(false);
        toast({ title: "Viewing History", description: "Loaded a game from the history." });
    }, 100);
  };

  const handleSpectateGame = useCallback((gameData: GameHistoryEntry) => {
    if (!game) return;
    resetGame();
    setSpectatedGameId(gameData.id);
    saveSpectatedGame(gameData.id);
    setCurrentGameState('starting');
    addLog(`SPECTATE: Connecting to live game ${gameData.id}...`);
    setTimeout(() => {
        loadPgnIntoGame(gameData.pgn);
        setIsGameStarted(true);
        setIsGameRunning(false);
        setCurrentGameState('running');
        setGameStatus(`Spectating Live Game: ${gameData.white} vs ${gameData.black}`);
        toast({ title: "Spectating Live Game", description: "You are now watching a game in progress." });

        // Setup SSE connection with enhanced error handling
        const setupSSE = () => {
            // Clean up existing connection
            if (eventSourceRef.current) {
                addLog(`🧹 SPECTATE: Closing existing SSE connection for game ${gameData.id}`);
                console.log(`🧹 CLIENT: Closing existing EventSource, readyState: ${eventSourceRef.current.readyState}`);
                eventSourceRef.current.close();
                eventSourceRef.current = null;
            }

            addLog(`🔄 SPECTATE: Connecting to SSE stream for game ${gameData.id}...`);
            console.log(`🔄 CLIENT: Creating new EventSource for game ${gameData.id}`);

            try {
                eventSourceRef.current = new EventSource(`/api/spectate/${gameData.id}`);
                console.log(`✅ CLIENT: EventSource created, readyState: ${eventSourceRef.current.readyState}`);

                eventSourceRef.current.onopen = (event) => {
                    addLog(`✅ SPECTATE: SSE connection established for game ${gameData.id}`);
                    console.log(`✅ CLIENT: SSE onopen fired, readyState: ${eventSourceRef.current?.readyState}`);
                    setIsReconnecting(false);
                    setCurrentGameState('running');
                };
            } catch (error) {
                addLog(`❌ SPECTATE_ERROR: Failed to create EventSource - ${error}`);
                console.error(`❌ CLIENT: EventSource creation failed:`, error);
                setCurrentGameState('error');
                toast({
                    variant: 'destructive',
                    title: "Connection Failed",
                    description: "Failed to establish connection to the live game."
                });
                return;
            }

            eventSourceRef.current.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    addLog(`📨 SPECTATE_UPDATE: Received ${data.type || 'update'} for game ${gameData.id}`);
                    console.log(`📨 CLIENT: SSE message received:`, data);

                    // Handle different message types
                    if (data.type === 'connected') {
                        addLog(`🎯 SPECTATE: Successfully connected to game ${gameData.id}`);
                        console.log(`🎯 CLIENT: Connection confirmed by server`);
                        setIsReconnecting(false);
                        setReconnectAttempts(0);
                        if (reconnectTimeoutRef.current) {
                            clearTimeout(reconnectTimeoutRef.current);
                            reconnectTimeoutRef.current = null;
                        }
                        return;
                    }

                    if (data.type === 'game_state') {
                        addLog(`📊 SPECTATE: Received initial game state for ${gameData.id}`);
                        // Load reasoning history into activities
                        if (data.reasoningHistory && Array.isArray(data.reasoningHistory)) {
                            data.reasoningHistory.forEach((reasoning: any) => {
                                if (reasoning.reasoning) {
                                    addGameActivity({
                                        type: 'reasoning',
                                        whitePlayer: gameData.white,
                                        blackPlayer: gameData.black,
                                        reasoning: reasoning.reasoning,
                                        move: reasoning.move,
                                        gameId: gameData.id
                                    });
                                }
                            });
                        }
                        return;
                    }

                    if (data.type === 'heartbeat') {
                        // Update heartbeat tracking
                        const now = Date.now();
                        setLastHeartbeat(now);

                        // Update connection health based on heartbeat timing
                        if (data.uptime && data.uptime < 10000) {
                            setConnectionHealth('good');
                        } else if (data.uptime && data.uptime < 30000) {
                            setConnectionHealth('poor');
                        } else {
                            setConnectionHealth('bad');
                        }

                        console.log(`💓 CLIENT: Heartbeat received for game ${gameData.id}, uptime: ${data.uptime}ms`);
                        return;
                    }

                    if (data.type === 'keep_alive') {
                        // Acknowledge keep-alive messages
                        setLastHeartbeat(Date.now());
                        console.log(`🔄 CLIENT: Keep-alive ${data.sequence} received for game ${gameData.id}`);
                        return;
                    }

                    if (data.type === 'error') {
                        addLog(`SPECTATE_ERROR: Server error - ${data.message}`);
                        setCurrentGameState('error');
                        toast({
                            variant: 'destructive',
                            title: "Game Error",
                            description: data.message || "An error occurred with the game connection."
                        });

                        if (data.message === 'Game not found') {
                            // Game doesn't exist, stop trying to reconnect
                            setSpectatedGameId(null);
                            saveSpectatedGame(null);
                            eventSourceRef.current?.close();
                        }
                        return;
                    }

                    // Handle game state updates (both old format and new format)
                    const pgn = data.pgn || data.newPgn;
                    const status = data.status;
                    const aiResult = data.aiResult;

                    if (pgn) {
                        // Update the current game board
                        loadPgnIntoGame(pgn);
                        
                        // If we have AI reasoning data, add it to the activity log
                        if (aiResult && aiResult.reasoning) {
                            addGameActivity({
                                type: 'reasoning',
                                whitePlayer: gameData.white,
                                blackPlayer: gameData.black,
                                reasoning: aiResult.reasoning,
                                move: aiResult.move,
                                gameId: gameData.id
                            });
                        }

                        // Update the specific game in history without refetching everything
                        setGameHistory(prev => prev.map(g =>
                            g.id === gameData.id
                                ? {...g, pgn: pgn, status: status as 'IN_PROGRESS' | 'COMPLETED' | 'FAILED'}
                                : g
                        ));

                        if (status === 'COMPLETED' || status === 'FAILED') {
                            addLog(`SPECTATE: Game ${gameData.id} has finished with status: ${status}.`);
                            setCurrentGameState(status === 'COMPLETED' ? 'completed' : 'error');
                            toast({
                                title: "Game Over",
                                description: `The spectated game has finished with status: ${status}.`,
                                duration: 3000
                            });
                            eventSourceRef.current?.close();
                            setSpectatedGameId(null);
                            saveSpectatedGame(null);
                        }
                    }
                } catch (error) {
                    addLog(`SPECTATE_ERROR: Failed to parse SSE message - ${error}`);
                }
            };

            eventSourceRef.current.onerror = (error) => {
                const readyState = eventSourceRef.current?.readyState;
                const stateNames = {
                    [EventSource.CONNECTING]: 'CONNECTING',
                    [EventSource.OPEN]: 'OPEN',
                    [EventSource.CLOSED]: 'CLOSED'
                };

                console.error(`❌ CLIENT: SSE error, readyState: ${readyState}, state: ${stateNames[readyState || -1] || 'UNKNOWN'}`);

                // Handle connection closed - attempt automatic reconnection with limits
                if (readyState === EventSource.CLOSED) {
                    console.log(`🔌 CLIENT: Connection closed for game ${gameData.id}`);
                    
                    // Only attempt reconnection if under max attempts and not already reconnecting
                    if (reconnectAttempts < maxReconnectAttempts && !isReconnecting) {
                        setIsReconnecting(true);
                        setReconnectAttempts(prev => prev + 1);
                        
                        const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 10000); // Exponential backoff, max 10s
                        addLog(`🔄 SPECTATE: Auto-reconnecting in ${delay/1000}s (attempt ${reconnectAttempts + 1}/${maxReconnectAttempts})`);
                        
                        reconnectTimeoutRef.current = setTimeout(() => {
                            if (eventSourceRef.current) {
                                eventSourceRef.current.close();
                                eventSourceRef.current = null;
                            }
                            setupSSE(); // Recursively call setupSSE
                        }, delay);
                    } else {
                        // Max attempts reached, require manual reconnection
                        addLog(`❌ SPECTATE: Max reconnection attempts reached for game ${gameData.id}`);
                        setIsReconnecting(false);
                        setCurrentGameState('error');
                        
                        toast({
                            title: "Connection Failed",
                            description: "Unable to maintain connection. Click Reconnect to try again.",
                            variant: "destructive",
                            duration: 8000,
                        });
                    }

                    // NO AUTOMATIC RECONNECTION - User must click reconnect button

                } else if (readyState === EventSource.CONNECTING) {
                    console.log(`⏳ CLIENT: In CONNECTING state, waiting...`);
                    
                    // Don't trigger multiple timeout handlers
                    if (!isReconnecting) {
                        setIsReconnecting(true);
                        addLog(`⏳ SPECTATE: Connecting to game ${gameData.id}...`);
                        
                        // Set timeout for connecting state
                        setTimeout(() => {
                            if (eventSourceRef.current?.readyState === EventSource.CONNECTING) {
                                addLog(`⏰ SPECTATE: Connection timeout for game ${gameData.id}`);
                                console.log(`⏰ CLIENT: Connection timeout, closing connection`);
                                eventSourceRef.current?.close();
                                setIsReconnecting(false);
                                setCurrentGameState('error');
                            }
                        }, 15000); // 15 second timeout for connecting state
                    }
                } else {
                    addLog(`❌ SPECTATE: Unexpected error state: ${readyState} for game ${gameData.id}`);
                    console.error(`❌ CLIENT: Unexpected readyState: ${readyState}`);
                    setCurrentGameState('error');
                    setIsReconnecting(false);
                    toast({
                        variant: 'destructive',
                        title: "Connection Error",
                        description: "Unexpected connection error. Use Reconnect button to retry."
                    });
                }
            };
        };

        setupSSE();
    }, 100);
  }, [game, resetGame, saveSpectatedGame, addLog, toast, loadPgnIntoGame, setGameHistory]);

  const handleDeleteGame = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this game?')) return;
    addLog(`Deleting game ${id}...`);
    try {
      await deleteGame(id);
      addLog(`Game ${id} deleted successfully.`);
      toast({ title: "Game Deleted" });

      // Remove the deleted game from local state without full refresh
      setGameHistory(prev => prev.filter(g => g.id !== id));
    } catch (error) {
      addLog(`ERROR_DELETING_GAME: ${error instanceof Error ? error.message : "Unknown error"}`);
      toast({ variant: 'destructive', title: "Delete Failed" });
    }
  };

  // Handler for spectating a game from live games manager
  const handleSpectateFromLiveGames = useCallback((gameId: string) => {
    // Find the game in history or create a minimal game object
    const existingGame = gameHistory.find(g => g.id === gameId);
    if (existingGame) {
      handleSpectateGame(existingGame);
    } else {
      // Create a minimal game object for spectating
      const gameToSpectate: GameHistoryEntry = {
        id: gameId,
        white: 'Unknown',
        black: 'Unknown',
        result: null,
        pgn: '',
        status: 'IN_PROGRESS',
        createdAt: new Date().toISOString(),
      };
      handleSpectateGame(gameToSpectate);
    }
    setShowLiveGames(false); // Close live games panel
  }, [gameHistory, handleSpectateGame]);

  // Handler for viewing a game from history
  const handleViewGameFromHistory = useCallback((gameId: string) => {
    const game = gameHistory.find(g => g.id === gameId);
    if (game) {
      if (game.status === 'IN_PROGRESS') {
        handleSpectateGame(game);
      } else {
        // Load completed game for viewing
        loadPgnIntoGame(game.pgn);
        addLog(`Loaded completed game: ${game.white} vs ${game.black}`);
        toast({
          title: "Game Loaded",
          description: `Viewing game: ${game.white} vs ${game.black}`,
        });
      }
    }
    setShowGameHistory(false); // Close history panel
  }, [gameHistory, handleSpectateGame, loadPgnIntoGame, addLog, toast]);
  
  const isHumanTurn = isGameRunning && gameMode === 'human-vs-ai' && game?.turn() === humanPlayer[0] && currentMoveNumber === game?.history().length;
  const boardOrientation = gameMode === 'human-vs-ai' ? humanPlayer : 'white';
  const totalMoves = game?.history().length ?? 0;

  // Effect to restore spectated game on page load
  useEffect(() => {
    const savedSpectatedGameId = getSpectatedGame();
    if (savedSpectatedGameId && gameHistory.length > 0 && !isReconnecting) {
      const savedGame = gameHistory.find(g => g.id === savedSpectatedGameId);
      if (savedGame && savedGame.status === 'IN_PROGRESS') {
        setIsReconnecting(true);
        addLog(`RESTORE: Reconnecting to spectated game ${savedSpectatedGameId}...`);
        toast({ 
          title: "Reconnecting to Live Game", 
          description: "Restoring your connection to the spectated game.",
          duration: 3000 
        });
        handleSpectateGame(savedGame);
        setTimeout(() => setIsReconnecting(false), 2000);
      } else if (savedGame && (savedGame.status === 'COMPLETED' || savedGame.status === 'FAILED')) {
        // Game finished, clear from localStorage and show notification
        saveSpectatedGame(null);
        toast({
          title: "Spectated Game Finished",
          description: `The game you were watching has ${savedGame.status.toLowerCase()}.`,
          duration: 3000
        });
      } else {
        // Game no longer exists, clear from localStorage
        saveSpectatedGame(null);
      }
    }
  }, [gameHistory, getSpectatedGame, saveSpectatedGame, addLog, toast, handleSpectateGame, isReconnecting]);

  useEffect(() => {
      fetchGameHistory();
      return () => {
          if (eventSourceRef.current) {
              eventSourceRef.current.close();
              eventSourceRef.current = null;
          }
          if (reconnectTimeoutRef.current) {
              clearTimeout(reconnectTimeoutRef.current);
              reconnectTimeoutRef.current = null;
          }
      };
  }, [fetchGameHistory]);

  return (
    <div className="container mx-auto p-4 max-w-7xl">
      <div className="w-full">
        <header className="text-center mb-4">
          <h1 className="text-3xl md:text-4xl font-bold text-primary mb-2">
            Chess Duel Arena
          </h1>
          <p className="text-sm text-muted-foreground">AI vs AI Chess Simulation</p>
        </header>

        <Card className="mb-4 shadow-lg">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Game Setup</CardTitle>
                <CardDescription>Configure your chess match.</CardDescription>
              </div>
              <div className="flex items-center gap-2">
                {spectatedGameId && (
                  <Badge
                    variant={connectionHealth === 'good' ? 'default' : connectionHealth === 'poor' ? 'secondary' : 'destructive'}
                    className="flex items-center gap-1"
                  >
                    <div className={`w-2 h-2 rounded-full ${
                      connectionHealth === 'good' ? 'bg-green-500 animate-pulse' :
                      connectionHealth === 'poor' ? 'bg-yellow-500' : 'bg-red-500'
                    }`} />
                    {connectionHealth === 'good' ? 'Connected' :
                     connectionHealth === 'poor' ? 'Unstable' : 'Poor Connection'}
                  </Badge>
                )}
                <Badge variant={getGameStateDisplay(currentGameState).variant} className="flex items-center gap-1">
                  {getGameStateDisplay(currentGameState).icon}
                  {getGameStateDisplay(currentGameState).text}
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-4 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>Mode</Label>
                <RadioGroup value={gameMode} onValueChange={(v) => setGameMode(v as GameMode)} className="space-y-2" disabled={isGameStarted}>
                  <div className="flex items-center space-x-2"><RadioGroupItem value="ai-vs-ai" id="ai-vs-ai" /><Label htmlFor="ai-vs-ai" className="flex items-center gap-1 text-sm"><Bot className="h-3 w-3" /> AI vs AI</Label></div>
                  <div className="flex items-center space-x-2"><RadioGroupItem value="human-vs-ai" id="human-vs-ai" /><Label htmlFor="human-vs-ai" className="flex items-center gap-1 text-sm"><User className="h-3 w-3" /> Human vs AI</Label></div>
                  <div className="flex items-center space-x-2"><RadioGroupItem value="freestyle-ai-vs-ai" id="freestyle-ai-vs-ai" /><Label htmlFor="freestyle-ai-vs-ai" className="flex items-center gap-1 text-sm"><Wand2 className="h-3 w-3" /> Chess960</Label></div>
                </RadioGroup>
              </div>
              
              {gameMode === 'human-vs-ai' && (
                <div className="space-y-2">
                  <Label>Play As</Label>
                  <RadioGroup value={humanPlayer} onValueChange={(v) => setHumanPlayer(v as Player)} className="space-y-2" disabled={isGameStarted}>
                    <div className="flex items-center space-x-2"><RadioGroupItem value="white" id="play-white" /><Label htmlFor="play-white" className="text-sm">White</Label></div>
                    <div className="flex items-center space-x-2"><RadioGroupItem value="black" id="play-black" /><Label htmlFor="play-black" className="text-sm">Black</Label></div>
                  </RadioGroup>
                </div>
              )}
              
              <div className="space-y-3">
                <div className="space-y-2">
                    <Label className="text-sm flex items-center gap-1">{gameMode === 'human-vs-ai' && humanPlayer === 'white' ? <User className="h-3 w-3" /> : <Bot className="h-3 w-3" />} White</Label>
                    { (gameMode !== 'human-vs-ai' || humanPlayer !== 'white') ? (
                        <Select value={whiteModel} onValueChange={(v) => setWhiteModel(v as Model)} disabled={isGameStarted}>
                          <SelectTrigger className="text-sm"><SelectValue /></SelectTrigger>
                          <SelectContent>
                            <div className="font-semibold text-xs text-muted-foreground px-2 py-1">Google AI</div>
                            {ALL_MODELS.filter(m => !m.startsWith('openrouter/')).map(m => <SelectItem key={m} value={m} className="text-xs">{m}</SelectItem>)}
                            <div className="font-semibold text-xs text-muted-foreground px-2 py-1 mt-2">OpenRouter</div>
                            {ALL_MODELS.filter(m => m.startsWith('openrouter/')).map(m => <SelectItem key={m} value={m} className="text-xs">{getModelDisplayName(m)}</SelectItem>)}
                          </SelectContent>
                        </Select>
                    ) : <div className="text-sm text-muted-foreground py-2">Human Player</div>}
                    {whiteEloRating && (
                      <ELODisplay
                        rating={whiteEloRating}
                        gamesPlayed={whiteProfile?.gamesPlayed}
                        eloChange={whiteEloChange}
                        opponentRating={blackEloRating}
                        playerName="White"
                        className="text-xs"
                      />
                    )}
                </div>
                <div className="space-y-2">
                    <Label className="text-sm flex items-center gap-1">{gameMode === 'human-vs-ai' && humanPlayer === 'black' ? <User className="h-3 w-3" /> : <Bot className="h-3 w-3" />} Black</Label>
                    { (gameMode !== 'human-vs-ai' || humanPlayer !== 'black') ? (
                        <Select value={blackModel} onValueChange={(v) => setBlackModel(v as Model)} disabled={isGameStarted}>
                          <SelectTrigger className="text-sm"><SelectValue /></SelectTrigger>
                          <SelectContent>
                            <div className="font-semibold text-xs text-muted-foreground px-2 py-1">Google AI</div>
                            {ALL_MODELS.filter(m => !m.startsWith('openrouter/')).map(m => <SelectItem key={m} value={m} className="text-xs">{m}</SelectItem>)}
                            <div className="font-semibold text-xs text-muted-foreground px-2 py-1 mt-2">OpenRouter</div>
                            {ALL_MODELS.filter(m => m.startsWith('openrouter/')).map(m => <SelectItem key={m} value={m} className="text-xs">{getModelDisplayName(m)}</SelectItem>)}
                          </SelectContent>
                        </Select>
                    ) : <div className="text-sm text-muted-foreground py-2">Human Player</div>}
                    {blackEloRating && (
                      <ELODisplay
                        rating={blackEloRating}
                        gamesPlayed={blackProfile?.gamesPlayed}
                        eloChange={blackEloChange}
                        opponentRating={whiteEloRating}
                        playerName="Black"
                        className="text-xs"
                      />
                    )}
                </div>
              </div>
            </div>
            
            <div className="flex justify-center gap-2 pt-2 border-t">
                <Button 
                  onClick={handleStartStop} 
                  disabled={(game?.isGameOver() && currentMoveNumber === totalMoves) || spectatedGameId !== null}
                  className={isGameRunning ? "bg-orange-600 hover:bg-orange-700" : ""}
                >
                  {!isGameStarted ? (
                    <>
                      <Play className="mr-2 h-4 w-4" /> 
                      Start Game
                    </>
                  ) : isGameRunning ? (
                    <>
                      <StopIcon className="mr-2 h-4 w-4" /> 
                      Stop Running Game
                    </>
                  ) : (
                    <>
                      <PlayCircle className="mr-2 h-4 w-4" /> 
                      Resume Game
                    </>
                  )}
                </Button>
                <Button onClick={() => resetGame()} variant="outline"><RotateCcw className="mr-2 h-4 w-4" /> Reset</Button>
                <Button
                  onClick={() => setShowLiveGames(!showLiveGames)}
                  variant="outline"
                  className="flex items-center gap-1"
                >
                  <Eye className="h-4 w-4" />
                  Live Games
                </Button>
                <Button
                  onClick={() => setShowGameHistory(!showGameHistory)}
                  variant="outline"
                  className="flex items-center gap-1"
                >
                  <HistoryIcon className="h-4 w-4" />
                  History
                </Button>
                {currentGameState === 'error' && spectatedGameId && (
                  <Button
                    onClick={() => {
                      const savedSpectatedGameId = getSpectatedGame();
                      if (savedSpectatedGameId) {
                        const savedGame = gameHistory.find(g => g.id === savedSpectatedGameId);
                        if (savedGame && savedGame.status === 'IN_PROGRESS') {
                          addLog(`🔄 MANUAL: Attempting manual reconnection to game ${savedGame.id}`);
                          // Reset reconnection attempts for manual retry
                          setReconnectAttempts(0);
                          if (reconnectTimeoutRef.current) {
                            clearTimeout(reconnectTimeoutRef.current);
                            reconnectTimeoutRef.current = null;
                          }
                          handleSpectateGame(savedGame);
                        } else {
                          fetchGameHistory();
                        }
                      } else {
                        fetchGameHistory();
                      }
                    }}
                    variant="destructive"
                    size="default"
                    disabled={isReconnecting}
                    className="animate-pulse"
                  >
                    <RefreshCw className={`mr-2 h-4 w-4 ${isReconnecting ? 'animate-spin' : ''}`} />
                    {isReconnecting ? 'Connecting...' : 'Reconnect to Live Game'}
                  </Button>
                )}
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-4">
          {/* Chess Board */}
          <div className="lg:col-span-2 xl:col-span-2">
            <Card className="shadow-lg overflow-hidden p-2">
              <div className="aspect-square w-full relative">
                <Chessboard position={fen} onPieceDrop={onPieceDrop} onSquareClick={onSquareClick} boardOrientation={boardOrientation} arePiecesDraggable={isHumanTurn}
                  customBoardStyle={{
                    borderRadius: '0.25rem',
                    boxShadow: 'none',
                    border: '1px solid hsl(var(--border))'
                  }}
                  customDarkSquareStyle={{ backgroundColor: 'hsl(var(--primary) / 0.6)' }}
                  customLightSquareStyle={{ backgroundColor: 'hsl(var(--secondary))' }}
                  customSquareStyles={{ ...optionSquares, ...(lastMove ? { [lastMove.from]: { backgroundColor: 'hsl(var(--accent) / 0.4)' }, [lastMove.to]: { backgroundColor: 'hsl(var(--accent) / 0.4)' } } : {}), ...(moveFrom && { [moveFrom]: { backgroundColor: 'hsl(var(--accent) / 0.6)' } })}}
                />
              </div>
            </Card>
          </div>

          {/* Move History Panel */}
          <div className="lg:col-span-1 xl:col-span-1">
            <Card className="shadow-lg h-[400px] lg:h-[600px] flex flex-col">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base flex items-center gap-2">
                    <HistoryIcon className="h-4 w-4" />
                    Move History
                  </CardTitle>
                  <div className="flex gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleCopy(fen)}
                      className="h-8 w-8 p-0"
                      title="Copy FEN"
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleCopy(pgn)}
                      className="h-8 w-8 p-0"
                      title="Copy PGN"
                    >
                      <Share2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
                <div className="flex items-center justify-center gap-1 flex-wrap">
                  <Button onClick={handlePreviousMove} disabled={!isGameStarted || currentMoveNumber === 0} variant="outline" size="sm">
                    <ChevronLeft className="h-4 w-4 mr-1" /> Prev
                  </Button>
                  <span className="text-sm text-muted-foreground font-mono px-2">
                    {currentMoveNumber} / {totalMoves}
                  </span>
                  <Button onClick={handleNextMove} disabled={!isGameStarted || currentMoveNumber === totalMoves} variant="outline" size="sm">
                    Next <ChevronRight className="h-4 w-4 ml-1" />
                  </Button>
                  <Button onClick={handleGoToLatest} disabled={!isGameStarted || currentMoveNumber === totalMoves} variant="secondary" size="sm">
                    Latest
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="flex-1 p-0 overflow-hidden">
                <ScrollArea className="h-full px-3">
                  {history.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <Clock className="w-6 h-6 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No moves yet</p>
                    </div>
                  ) : (
                    <div className="space-y-1">
                      {history.map((h, i) => (
                        <div
                          key={`move-history-${i}`}
                          className={`p-2 rounded-md border cursor-pointer hover:bg-accent/10 transition-colors ${
                            activeAccordionItem === `item-${i}` ? 'bg-accent/20 border-accent' : 'bg-card'
                          }`}
                          onClick={() => {
                            setActiveAccordionItem(activeAccordionItem === `item-${i}` ? null : `item-${i}`);
                            navigateToMove(i * 2 + (h.blackMove ? 2 : 1));
                          }}
                        >
                          <div className="text-sm font-medium">
                            {`${h.moveNumber}. ${h.whiteMove} ${h.blackMove || ''}`}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Click to view reasoning
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </CardContent>
            </Card>
          </div>

          {/* AI Reasoning Panel */}
          <div className="lg:col-span-1 xl:col-span-1">
            <Card className="shadow-lg h-[400px] lg:h-[600px] flex flex-col">
              <CardHeader className="pb-3">
                <CardTitle className="text-base flex items-center gap-2">
                  <Bot className="h-4 w-4" />
                  AI Reasoning
                </CardTitle>
                <CardDescription className="flex items-center gap-2 min-h-[20px]">
                  {(isThinking || currentGameState === 'processing-move') && <Loader2 className="animate-spin h-4 w-4" />}
                  {currentGameState === 'processing-move' && !spectatedGameId && (
                    <Badge variant="default" className="animate-pulse">
                      AI Thinking...
                    </Badge>
                  )}
                  {isReconnecting && (
                    <Badge variant="default" className="animate-pulse">
                      <Loader2 className="h-3 w-3 animate-spin mr-1" />
                      Reconnecting...
                    </Badge>
                  )}
                  <span className="flex-1">{gameStatus}</span>
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-1 p-0 overflow-hidden">
                <ScrollArea className="h-full px-3">
                  {!activeAccordionItem ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <Eye className="w-6 h-6 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">Select a move to view AI reasoning</p>
                    </div>
                  ) : (
                    (() => {
                      const moveIndex = parseInt(activeAccordionItem.replace('item-', ''));
                      const move = history[moveIndex];
                      if (!move) return null;

                      return (
                        <div className="space-y-4 py-2">
                          {/* White Move Reasoning */}
                          {move.whiteMove !== '...' && (
                            <div className="space-y-3">
                              <div className="flex items-center gap-2">
                                <div className="w-6 h-6 rounded-full bg-white border-2 border-gray-300 flex items-center justify-center">
                                  <span className="text-xs font-bold text-black">W</span>
                                </div>
                                <div>
                                  <p className="font-semibold text-sm">{move.whiteMove}</p>
                                  <p className="text-xs text-muted-foreground">White&apos;s move</p>
                                </div>
                              </div>
                              <div className="pl-8 space-y-2">
                                <div className="text-sm text-muted-foreground">
                                  <span className="font-medium">Reasoning:</span>
                                  <p className="mt-1">{move.whiteReason || 'No reasoning available'}</p>
                                </div>
                                {move.whiteOpponentPrediction && (
                                  <div className="text-sm text-muted-foreground">
                                    <span className="font-medium">Opponent Prediction:</span>
                                    <p className="mt-1">{move.whiteOpponentPrediction}</p>
                                  </div>
                                )}
                                {move.whiteAnalysis && move.whiteAnalysis.length > 0 && (
                                  <div className="text-sm">
                                    <span className="font-medium">Alternative Moves:</span>
                                    <div className="mt-1 space-y-1">
                                      {move.whiteAnalysis.map((a, idx) => (
                                        <div key={idx} className="pl-2 border-l-2 border-muted space-y-1">
                                          <p className="font-medium text-xs">{a.move}</p>
                                          <p className="text-xs text-green-600">✓ {a.consideration}</p>
                                          <p className="text-xs text-red-600">✗ {a.rejectionReason}</p>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          )}

                          {/* Black Move Reasoning */}
                          {move.blackMove && (
                            <div className="space-y-3 pt-4 border-t">
                              <div className="flex items-center gap-2">
                                <div className="w-6 h-6 rounded-full bg-black border-2 border-gray-300 flex items-center justify-center">
                                  <span className="text-xs font-bold text-white">B</span>
                                </div>
                                <div>
                                  <p className="font-semibold text-sm">{move.blackMove}</p>
                                  <p className="text-xs text-muted-foreground">Black&apos;s move</p>
                                </div>
                              </div>
                              <div className="pl-8 space-y-2">
                                <div className="text-sm text-muted-foreground">
                                  <span className="font-medium">Reasoning:</span>
                                  <p className="mt-1">{move.blackReason || 'No reasoning available'}</p>
                                </div>
                                {move.blackOpponentPrediction && (
                                  <div className="text-sm text-muted-foreground">
                                    <span className="font-medium">Opponent Prediction:</span>
                                    <p className="mt-1">{move.blackOpponentPrediction}</p>
                                  </div>
                                )}
                                {move.blackAnalysis && move.blackAnalysis.length > 0 && (
                                  <div className="text-sm">
                                    <span className="font-medium">Alternative Moves:</span>
                                    <div className="mt-1 space-y-1">
                                      {move.blackAnalysis.map((a, idx) => (
                                        <div key={idx} className="pl-2 border-l-2 border-muted space-y-1">
                                          <p className="font-medium text-xs">{a.move}</p>
                                          <p className="text-xs text-green-600">✓ {a.consideration}</p>
                                          <p className="text-xs text-red-600">✗ {a.rejectionReason}</p>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })()
                  )}
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </div>
        
        {/* Developer Logs - Collapsible */}
        <div className="mt-4">
          <details className="group">
            <summary className="cursor-pointer text-sm text-muted-foreground hover:text-foreground transition-colors flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Developer Logs ({logs.length})
              <span className="text-xs opacity-60">(Click to expand)</span>
            </summary>
            <Card className="mt-2 shadow-sm">
              <CardContent className="p-3">
                <ScrollArea className="h-24 rounded-md border p-2 font-mono text-xs bg-muted/30">
                  {logs.length === 0 ? (
                    <p className="text-muted-foreground text-center py-2">No logs yet</p>
                  ) : (
                    logs.map((log, i) => <p key={i} className="whitespace-pre-wrap break-all">{log}</p>)
                  )}
                </ScrollArea>
              </CardContent>
            </Card>
          </details>
        </div>

        {/* Live Games Manager Modal */}
        {showLiveGames && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-background rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
              <div className="flex items-center justify-between p-4 border-b">
                <h2 className="text-lg font-semibold">Live Games</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowLiveGames(false)}
                >
                  ✕
                </Button>
              </div>
              <div className="p-4">
                <LiveGamesManager onSpectateGame={handleSpectateFromLiveGames} />
              </div>
            </div>
          </div>
        )}

        {/* Game History Modal */}
        {showGameHistory && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-background rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
              <div className="flex items-center justify-between p-4 border-b">
                <h2 className="text-lg font-semibold">Game History</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowGameHistory(false)}
                >
                  ✕
                </Button>
              </div>
              <div className="p-4">
                <GameHistory onViewGame={handleViewGameFromHistory} />
              </div>
            </div>
          </div>
        )}

      </div>
    </div>
  );
}