"use client";

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Trophy, TrendingUp, TrendingDown, AlertTriangle, Target } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ELODisplayProps {
  rating: number;
  gamesPlayed?: number;
  eloChange?: number;
  opponentRating?: number;
  playerName?: string;
  className?: string;
}

// Calculate expected win probability using ELO formula
function calculateWinProbability(playerElo: number, opponentElo: number): number {
  return 1 / (1 + Math.pow(10, (opponentElo - playerElo) / 400));
}

// Determine volatility status based on games played
function getVolatilityStatus(gamesPlayed: number): { status: 'volatile' | 'stabilizing' | 'stable', label: string, color: string } {
  if (gamesPlayed < 5) {
    return { status: 'volatile', label: 'Volatile', color: 'text-red-500' };
  } else if (gamesPlayed < 10) {
    return { status: 'stabilizing', label: 'Stabilizing', color: 'text-yellow-500' };
  } else {
    return { status: 'stable', label: 'Stable', color: 'text-green-500' };
  }
}

export function ELODisplay({ 
  rating, 
  gamesPlayed, 
  eloChange, 
  opponentRating, 
  playerName,
  className 
}: ELODisplayProps) {
  const volatility = gamesPlayed !== undefined ? getVolatilityStatus(gamesPlayed) : null;
  const winProbability = opponentRating ? calculateWinProbability(rating, opponentRating) : null;

  return (
    <TooltipProvider>
      <div className={cn("space-y-1", className)}>
        {/* Main ELO Display */}
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <Trophy className="h-3 w-3 text-amber-500" />
            <span className="font-medium text-sm">{rating}</span>
          </div>
          
          {/* ELO Change Indicator */}
          {eloChange !== undefined && eloChange !== 0 && (
            <div className={cn(
              "flex items-center gap-1 text-xs font-medium",
              eloChange > 0 ? "text-green-600" : "text-red-600"
            )}>
              {eloChange > 0 ? (
                <TrendingUp className="h-3 w-3" />
              ) : (
                <TrendingDown className="h-3 w-3" />
              )}
              <span>{eloChange > 0 ? '+' : ''}{eloChange}</span>
            </div>
          )}
          
          {/* Volatility Status */}
          {volatility && (
            <Tooltip>
              <TooltipTrigger>
                <div className="flex items-center gap-1">
                  <AlertTriangle className={cn("h-3 w-3", volatility.color)} />
                  <Badge variant="outline" className="text-xs px-1 py-0">
                    {volatility.label}
                  </Badge>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-xs">
                  {volatility.status === 'volatile' && 'High rating volatility (< 5 games)'}
                  {volatility.status === 'stabilizing' && 'Rating stabilizing (5-9 games)'}
                  {volatility.status === 'stable' && 'Stable rating (10+ games)'}
                </p>
              </TooltipContent>
            </Tooltip>
          )}
        </div>

        {/* Additional Info */}
        <div className="flex items-center gap-3 text-xs text-muted-foreground">
          {/* Games Played */}
          {gamesPlayed !== undefined && (
            <span>{gamesPlayed} games</span>
          )}
          
          {/* Win Probability */}
          {winProbability !== null && (
            <Tooltip>
              <TooltipTrigger>
                <div className="flex items-center gap-1">
                  <Target className="h-3 w-3" />
                  <span>{(winProbability * 100).toFixed(0)}%</span>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-xs">
                  Expected win probability vs opponent ({opponentRating} ELO)
                </p>
              </TooltipContent>
            </Tooltip>
          )}
        </div>
      </div>
    </TooltipProvider>
  );
}

// Simplified version for inline display
export function ELOBadge({ rating, eloChange, className }: { rating: number, eloChange?: number, className?: string }) {
  return (
    <div className={cn("flex items-center gap-1", className)}>
      <Trophy className="h-3 w-3 text-amber-500" />
      <span className="font-medium text-sm">{rating}</span>
      {eloChange !== undefined && eloChange !== 0 && (
        <span className={cn(
          "text-xs font-medium",
          eloChange > 0 ? "text-green-600" : "text-red-600"
        )}>
          ({eloChange > 0 ? '+' : ''}{eloChange})
        </span>
      )}
    </div>
  );
}

export default ELODisplay;