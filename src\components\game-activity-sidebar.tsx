"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { 
  Clock, 
  Trophy, 
  TrendingUp, 
  TrendingDown, 
  Minus,
  Bot,
  Zap,
  Eye,
  RefreshCw
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface GameActivity {
  id: string;
  timestamp: Date;
  type: 'game_start' | 'game_end' | 'elo_update' | 'move' | 'reasoning';
  whitePlayer: string;
  blackPlayer: string;
  whiteElo?: number;
  blackElo?: number;
  whiteEloChange?: number;
  blackEloChange?: number;
  result?: '1-0' | '0-1' | '1/2-1/2';
  move?: string;
  reasoning?: string;
  gameId?: string;
}

interface GameActivitySidebarProps {
  className?: string;
}

export function GameActivitySidebar({ className }: GameActivitySidebarProps) {
  const [activities, setActivities] = useState<GameActivity[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Load activities from database on mount
  useEffect(() => {
    refreshActivities();
  }, []);

  // Save activities to localStorage whenever activities change
  useEffect(() => {
    localStorage.setItem('gameActivities', JSON.stringify(activities));
  }, [activities]);

  // Function to add new activity
  const addActivity = async (activity: Omit<GameActivity, 'id' | 'timestamp'>) => {
    const newActivity: GameActivity = {
      ...activity,
      id: Date.now().toString(),
      timestamp: new Date(),
    };

    // Add to local state immediately for real-time updates
    setActivities(prev => [newActivity, ...prev].slice(0, 100)); // Keep only last 100 activities

    // Save to database
    try {
      await fetch('/api/game-activity', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: activity.type,
          whitePlayer: activity.whitePlayer,
          blackPlayer: activity.blackPlayer,
          whiteElo: activity.whiteElo,
          blackElo: activity.blackElo,
          whiteEloChange: activity.whiteEloChange,
          blackEloChange: activity.blackEloChange,
          result: activity.result,
          move: activity.move,
          reasoning: activity.reasoning,
          gameId: activity.gameId,
        }),
      });
    } catch (error) {
      console.error('Error saving game activity to database:', error);
    }
  };

  // Function to clear all activities
  const clearActivities = () => {
    setActivities([]);
    localStorage.removeItem('gameActivities');
  };

  // Function to refresh activities from database
  const refreshActivities = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/game-activity?limit=100');
      if (response.ok) {
        const data = await response.json();
        const dbActivities = data.activities.map((a: any) => ({
          ...a,
          timestamp: new Date(a.createdAt)
        }));
        setActivities(dbActivities);
      }
    } catch (error) {
      console.error('Error fetching activities from database:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Expose addActivity function globally for chess component to use
  useEffect(() => {
    (window as any).addGameActivity = addActivity;
    return () => {
      delete (window as any).addGameActivity;
    };
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  };

  const getActivityIcon = (type: GameActivity['type']) => {
    switch (type) {
      case 'game_start':
        return <Zap className="w-4 h-4 text-green-500" />;
      case 'game_end':
        return <Trophy className="w-4 h-4 text-blue-500" />;
      case 'elo_update':
        return <TrendingUp className="w-4 h-4 text-purple-500" />;
      case 'move':
        return <Bot className="w-4 h-4 text-gray-500" />;
      case 'reasoning':
        return <Eye className="w-4 h-4 text-orange-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getResultBadge = (result: string | undefined) => {
    if (!result) return null;
    
    let variant: "default" | "secondary" | "destructive" | "outline" = "secondary";
    let text = "Draw";
    
    if (result === '1-0') {
      text = "1-0";
      variant = "default";
    } else if (result === '0-1') {
      text = "0-1";
      variant = "destructive";
    } else if (result === '1/2-1/2') {
      text = "Draw";
      variant = "secondary";
    }
    
    return <Badge variant={variant} className="text-xs">{text}</Badge>;
  };

  const formatEloChange = (change: number | undefined) => {
    if (change === undefined) return null;
    const sign = change >= 0 ? "+" : "";
    const color = change >= 0 ? "text-green-600" : "text-red-600";
    return <span className={cn("text-xs font-medium", color)}>{sign}{change}</span>;
  };

  const renderActivity = (activity: GameActivity) => {
    switch (activity.type) {
      case 'game_start':
        return (
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              {getActivityIcon(activity.type)}
              <span className="text-xs sm:text-sm font-medium">Game Started</span>
            </div>
            <div className="text-xs text-muted-foreground truncate">
              {activity.whitePlayer} vs {activity.blackPlayer}
            </div>
            {activity.whiteElo && activity.blackElo && (
              <div className="text-xs text-muted-foreground">
                ELO: {activity.whiteElo} vs {activity.blackElo}
              </div>
            )}
          </div>
        );

      case 'game_end':
        return (
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              {getActivityIcon(activity.type)}
              <span className="text-sm font-medium">Game Ended</span>
              {getResultBadge(activity.result)}
            </div>
            <div className="text-xs text-muted-foreground">
              {activity.whitePlayer} vs {activity.blackPlayer}
            </div>
          </div>
        );

      case 'elo_update':
        return (
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              {getActivityIcon(activity.type)}
              <span className="text-sm font-medium">ELO Updated</span>
            </div>
            <div className="text-xs space-y-1">
              <div className="flex justify-between">
                <span>{activity.whitePlayer}:</span>
                {formatEloChange(activity.whiteEloChange)}
              </div>
              <div className="flex justify-between">
                <span>{activity.blackPlayer}:</span>
                {formatEloChange(activity.blackEloChange)}
              </div>
            </div>
          </div>
        );

      case 'move':
        return (
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              {getActivityIcon(activity.type)}
              <span className="text-sm font-medium">Move: {activity.move}</span>
            </div>
            <div className="text-xs text-muted-foreground">
              {activity.whitePlayer} vs {activity.blackPlayer}
            </div>
          </div>
        );

      case 'reasoning':
        return (
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              {getActivityIcon(activity.type)}
              <span className="text-xs sm:text-sm font-medium">AI Reasoning</span>
            </div>
            <div className="text-xs text-muted-foreground line-clamp-2 sm:line-clamp-3">
              {activity.reasoning}
            </div>
          </div>
        );

      default:
        return (
          <div className="text-sm text-muted-foreground">
            Unknown activity type
          </div>
        );
    }
  };

  return (
    <Card className={cn("h-full flex flex-col", className)}>
      <CardHeader className="pb-3 px-3 sm:px-6">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-base sm:text-lg">Game Activity</CardTitle>
            <CardDescription className="text-xs sm:text-sm">Real-time events & AI reasoning</CardDescription>
          </div>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={refreshActivities}
              disabled={isLoading}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={cn("w-3 h-3", isLoading && "animate-spin")} />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearActivities}
              disabled={activities.length === 0}
              className="h-8 px-2 text-xs"
            >
              Clear
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="flex-1 p-0">
        <ScrollArea className="h-full px-3 sm:px-6 pb-3 sm:pb-6">
          {activities.length === 0 ? (
            <div className="text-center py-6 sm:py-8 text-muted-foreground">
              <Clock className="w-6 h-6 sm:w-8 sm:h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No activity yet</p>
              <p className="text-xs">Start a game to see updates</p>
            </div>
          ) : (
            <div className="space-y-3 sm:space-y-4">
              {activities.map((activity, index) => (
                <div key={activity.id}>
                  <div className="flex items-start gap-2 sm:gap-3">
                    <div className="text-xs text-muted-foreground mt-1 min-w-[50px] sm:min-w-[60px]">
                      {formatTime(activity.timestamp)}
                    </div>
                    <div className="flex-1 min-w-0">
                      {renderActivity(activity)}
                    </div>
                  </div>
                  {index < activities.length - 1 && (
                    <Separator className="mt-3 sm:mt-4" />
                  )}
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}

export default GameActivitySidebar;
