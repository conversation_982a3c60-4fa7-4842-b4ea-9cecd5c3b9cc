"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { 
  Search, 
  Filter, 
  Download, 
  Calendar as CalendarIcon, 
  Trophy, 
  TrendingUp, 
  Users,
  Eye,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import {
  MatchHistoryEntry,
  MatchHistoryFilters,
  MatchStatistics
} from '@/lib/match-history-service';
import { profileService, LLMProfile } from '@/lib/profile-client';

interface HistoryManagerProps {
  className?: string;
}

export function HistoryManager({ className }: HistoryManagerProps) {
  const [matches, setMatches] = useState<MatchHistoryEntry[]>([]);
  const [profiles, setProfiles] = useState<LLMProfile[]>([]);
  const [statistics, setStatistics] = useState<MatchStatistics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState<MatchHistoryFilters>({
    limit: 20,
    offset: 0
  });
  const [selectedProfile, setSelectedProfile] = useState<string>('');
  const [dateFrom, setDateFrom] = useState<Date>();
  const [dateTo, setDateTo] = useState<Date>();
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    loadProfiles();
    loadMatches();
  }, []);

  useEffect(() => {
    loadMatches();
  }, [filters]);

  useEffect(() => {
    if (selectedProfile) {
      loadStatistics();
    }
  }, [selectedProfile]);

  const loadProfiles = async () => {
    try {
      const profilesData = await profileService.getAllProfiles();
      setProfiles(profilesData);
    } catch (error) {
      console.error('Error loading profiles:', error);
    }
  };

  const loadMatches = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams();
      if (filters.profileId) params.append('profileId', filters.profileId);
      if (filters.tournamentId) params.append('tournamentId', filters.tournamentId);
      if (filters.result) params.append('result', filters.result);
      if (filters.dateFrom) params.append('dateFrom', filters.dateFrom.toISOString());
      if (filters.dateTo) params.append('dateTo', filters.dateTo.toISOString());
      if (filters.limit) params.append('limit', filters.limit.toString());
      if (filters.offset) params.append('offset', filters.offset.toString());

      const response = await fetch(`/api/matches?${params.toString()}`);
      if (!response.ok) throw new Error('Failed to fetch matches');

      const matchesData = await response.json();
      setMatches(matchesData);

      // Calculate total pages (simplified - in real app you'd get total count from API)
      const estimatedTotal = matchesData.length === filters.limit ? (currentPage + 1) * filters.limit! : currentPage * filters.limit!;
      setTotalPages(Math.ceil(estimatedTotal / filters.limit!));
    } catch (error) {
      console.error('Error loading matches:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadStatistics = async () => {
    if (!selectedProfile) return;

    try {
      const response = await fetch(`/api/matches/statistics?profileId=${selectedProfile}`);
      if (!response.ok) throw new Error('Failed to fetch statistics');

      const stats = await response.json();
      setStatistics(stats);
    } catch (error) {
      console.error('Error loading statistics:', error);
    }
  };

  const handleFilterChange = (newFilters: Partial<MatchHistoryFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters, offset: 0 }));
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    setFilters(prev => ({ ...prev, offset: (page - 1) * prev.limit! }));
  };

  const getResultBadge = (result: string | null, isWhite: boolean, profileId?: string) => {
    if (!result) return <Badge variant="secondary">Unknown</Badge>;
    
    let variant: "default" | "secondary" | "destructive" | "outline" = "secondary";
    let text = "Draw";
    
    if (result === '1-0') {
      text = isWhite ? "Win" : "Loss";
      variant = isWhite ? "default" : "destructive";
    } else if (result === '0-1') {
      text = isWhite ? "Loss" : "Win";
      variant = isWhite ? "destructive" : "default";
    } else if (result === '1/2-1/2') {
      text = "Draw";
      variant = "secondary";
    }
    
    return <Badge variant={variant}>{text}</Badge>;
  };

  const formatEloChange = (change: number | null) => {
    if (change === null) return "—";
    const sign = change >= 0 ? "+" : "";
    const color = change >= 0 ? "text-green-600" : "text-red-600";
    return <span className={color}>{sign}{change}</span>;
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Match History</h2>
          <p className="text-muted-foreground">
            View comprehensive match history and analytics
          </p>
        </div>
        <Button variant="outline">
          <Download className="w-4 h-4 mr-2" />
          Export Data
        </Button>
      </div>

      <Tabs defaultValue="history" className="space-y-4">
        <TabsList>
          <TabsTrigger value="history">Match History</TabsTrigger>
          <TabsTrigger value="statistics">Statistics</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="history" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Filter className="w-5 h-5" />
                Filters
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label>Profile</Label>
                  <Select
                    value={selectedProfile}
                    onValueChange={(value) => {
                      const profileId = value === 'all' ? '' : value;
                      setSelectedProfile(profileId);
                      handleFilterChange({ profileId: profileId || undefined });
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All profiles" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All profiles</SelectItem>
                      {profiles.map(profile => (
                        <SelectItem key={profile.id} value={profile.id}>
                          {profile.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Result</Label>
                  <Select onValueChange={(value) => {
                    const result = value === 'all' ? undefined : value as any;
                    handleFilterChange({ result });
                  }}>
                    <SelectTrigger>
                      <SelectValue placeholder="All results" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All results</SelectItem>
                      <SelectItem value="win">Wins</SelectItem>
                      <SelectItem value="loss">Losses</SelectItem>
                      <SelectItem value="draw">Draws</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Date Range</Label>
                  <div className="flex gap-2">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="outline" className="flex-1">
                          <CalendarIcon className="w-4 h-4 mr-2" />
                          {dateFrom ? format(dateFrom, "MMM dd") : "From"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={dateFrom}
                          onSelect={(date) => {
                            setDateFrom(date);
                            handleFilterChange({ dateFrom: date });
                          }}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="outline" className="flex-1">
                          <CalendarIcon className="w-4 h-4 mr-2" />
                          {dateTo ? format(dateTo, "MMM dd") : "To"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={dateTo}
                          onSelect={(date) => {
                            setDateTo(date);
                            handleFilterChange({ dateTo: date });
                          }}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Match History Table */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Matches</CardTitle>
              <CardDescription>
                {matches.length} matches found
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-muted-foreground">Loading matches...</div>
                </div>
              ) : matches.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No matches found with current filters
                </div>
              ) : (
                <>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>Tournament</TableHead>
                        <TableHead>White</TableHead>
                        <TableHead>Black</TableHead>
                        <TableHead>Result</TableHead>
                        <TableHead>ELO Changes</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {matches.map((match) => (
                        <TableRow key={match.id}>
                          <TableCell>
                            {match.completedAt ? format(new Date(match.completedAt), "MMM dd, HH:mm") : "—"}
                          </TableCell>
                          <TableCell>
                            {match.tournament ? (
                              <div className="flex items-center gap-1">
                                <Trophy className="w-4 h-4" />
                                {match.tournament.name}
                              </div>
                            ) : (
                              "Casual"
                            )}
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">{match.whiteProfile.name}</div>
                              <div className="text-sm text-muted-foreground">
                                ELO: {match.whiteProfile.eloRating}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">{match.blackProfile.name}</div>
                              <div className="text-sm text-muted-foreground">
                                ELO: {match.blackProfile.eloRating}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            {getResultBadge(match.game.result, true)}
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <div>White: {formatEloChange(match.whiteEloChange)}</div>
                              <div>Black: {formatEloChange(match.blackEloChange)}</div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Button variant="ghost" size="sm">
                              <Eye className="w-4 h-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>

                  {/* Pagination */}
                  <div className="flex items-center justify-between mt-4">
                    <div className="text-sm text-muted-foreground">
                      Page {currentPage} of {totalPages}
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                      >
                        <ChevronLeft className="w-4 h-4" />
                        Previous
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                      >
                        Next
                        <ChevronRight className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="statistics">
          <Card>
            <CardHeader>
              <CardTitle>Statistics</CardTitle>
              <CardDescription>
                Performance statistics for selected profile
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!selectedProfile ? (
                <div className="text-center py-8 text-muted-foreground">
                  Select a profile to view statistics
                </div>
              ) : statistics ? (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold">{statistics.totalMatches}</div>
                    <div className="text-sm text-muted-foreground">Total Matches</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{statistics.winRate.toFixed(1)}%</div>
                    <div className="text-sm text-muted-foreground">Win Rate</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">{statistics.averageEloChange > 0 ? '+' : ''}{statistics.averageEloChange.toFixed(1)}</div>
                    <div className="text-sm text-muted-foreground">Avg ELO Change</div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  Loading statistics...
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>Analytics</CardTitle>
              <CardDescription>
                Advanced analytics and performance trends
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                Analytics dashboard coming soon...
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default HistoryManager;
