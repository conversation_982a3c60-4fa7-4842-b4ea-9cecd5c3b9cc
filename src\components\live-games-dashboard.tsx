"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Activity, 
  RefreshCw, 
  Play, 
  Clock, 
  Users, 
  Eye,
  CheckCircle,
  XCircle,
  Pause
} from 'lucide-react';
import { gameService, Game } from '@/lib/game-client';
import { GameStatusService } from '@/lib/game-status-service';

interface LiveGamesDashboardProps {
  className?: string;
  onSpectateGame?: (gameId: string) => void;
}

export function LiveGamesDashboard({ className, onSpectateGame }: LiveGamesDashboardProps) {
  const [liveGames, setLiveGames] = useState<Game[]>([]);
  const [completedGames, setCompletedGames] = useState<Game[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [gameStats, setGameStats] = useState<any>(null);

  const loadGames = async () => {
    setIsLoading(true);
    try {
      // Load live games
      const liveResponse = await gameService.getLiveGames(10);
      setLiveGames(liveResponse.games);

      // Load recent completed games
      const completedResponse = await gameService.getCompletedGames(5);
      setCompletedGames(completedResponse.games);

      // Load game statistics
      try {
        const stats = await GameStatusService.getGameStatistics();
        setGameStats(stats);
      } catch (statsError) {
        console.warn('Failed to load game statistics:', statsError);
      }

      setLastUpdate(new Date());
    } catch (error) {
      console.error('Error loading games:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadGames();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(loadGames, 30000);
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'IN_PROGRESS':
        return <Play className="h-4 w-4 text-green-500" />;
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4 text-blue-500" />;
      case 'FAILED':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Pause className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const badge = gameService.getGameStatusBadge(status);
    return (
      <Badge 
        variant={badge.variant as any}
        className="text-xs"
      >
        {badge.text}
      </Badge>
    );
  };

  const formatTimeAgo = (date: string) => {
    const now = new Date();
    const gameDate = new Date(date);
    const diffMs = now.getTime() - gameDate.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Games Dashboard
              </CardTitle>
              <CardDescription>
                Live and recent games overview
                {lastUpdate && (
                  <span className="ml-2 text-xs text-muted-foreground">
                    Last updated: {lastUpdate.toLocaleTimeString()}
                  </span>
                )}
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={loadGames}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Live Games Section */}
          <div>
            <div className="flex items-center gap-2 mb-3">
              <Play className="h-4 w-4 text-green-500" />
              <h3 className="font-semibold">Live Games</h3>
              <Badge variant="default" className="ml-auto">
                {liveGames.length}
              </Badge>
            </div>
            
            {liveGames.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No live games currently</p>
              </div>
            ) : (
              <ScrollArea className="h-48">
                <div className="space-y-2">
                  {liveGames.map((game) => (
                    <div
                      key={game.id}
                      className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                    >
                      <div className="flex items-center gap-3">
                        {getStatusIcon(game.status)}
                        <div>
                          <div className="font-medium text-sm">
                            {game.whitePlayer} vs {game.blackPlayer}
                          </div>
                          <div className="text-xs text-muted-foreground flex items-center gap-2">
                            <Clock className="h-3 w-3" />
                            {formatTimeAgo(game.createdAt)}
                            <Users className="h-3 w-3 ml-2" />
                            {GameStatusService.getSpectatorCount(game.id)} watching
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {getStatusBadge(game.status)}
                        {onSpectateGame && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onSpectateGame(game.id)}
                          >
                            <Eye className="h-3 w-3 mr-1" />
                            Watch
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            )}
          </div>

          <Separator />

          {/* Recent Completed Games */}
          <div>
            <div className="flex items-center gap-2 mb-3">
              <CheckCircle className="h-4 w-4 text-blue-500" />
              <h3 className="font-semibold">Recent Completed</h3>
              <Badge variant="secondary" className="ml-auto">
                {completedGames.length}
              </Badge>
            </div>
            
            {completedGames.length === 0 ? (
              <div className="text-center py-4 text-muted-foreground">
                <p className="text-sm">No recent completed games</p>
              </div>
            ) : (
              <div className="space-y-2">
                {completedGames.slice(0, 3).map((game) => (
                  <div
                    key={game.id}
                    className="flex items-center justify-between p-2 border rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      {getStatusIcon(game.status)}
                      <div>
                        <div className="font-medium text-sm">
                          {game.whitePlayer} vs {game.blackPlayer}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {gameService.getResultDisplay(game.result)} • {formatTimeAgo(game.createdAt)}
                        </div>
                      </div>
                    </div>
                    {getStatusBadge(game.status)}
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
