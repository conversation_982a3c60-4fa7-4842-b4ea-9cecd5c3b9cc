import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { 
  Play, 
  Eye, 
  Clock, 
  Users, 
  Trophy,
  RefreshCw,
  Loader2,
  Calendar,
  Target,
  Activity,
  Pause,
  Trash2,
  MoreHorizontal
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { gameService, Game } from '@/lib/game-client';

interface LiveGamesManagerProps {
  onSpectateGame?: (gameId: string) => void;
}

export function LiveGamesManager({ onSpectateGame }: LiveGamesManagerProps) {
  const [liveGames, setLiveGames] = useState<Game[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const loadLiveGames = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await gameService.getLiveGames(50); // Get up to 50 live games
      setLiveGames(response.games);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load live games';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadLiveGames();
    
    // Auto-refresh every 10 seconds
    const interval = setInterval(loadLiveGames, 10000);
    
    return () => clearInterval(interval);
  }, []);

  const handleSpectate = (gameId: string) => {
    if (onSpectateGame) {
      onSpectateGame(gameId);
    }
    toast({
      title: "Spectating Game",
      description: "Connecting to live game...",
    });
  };

  const handlePauseGame = async (gameId: string, gameName: string) => {
    if (!window.confirm(`Are you sure you want to stop the game "${gameName}"?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/games/${gameId}/pause`, {
        method: 'POST',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to stop game');
      }

      toast({
        title: "Game Stopped",
        description: "The live game has been stopped successfully.",
      });

      // Refresh the live games list
      loadLiveGames();
    } catch (error) {
      console.error('Error stopping game:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to stop game",
        variant: "destructive",
      });
    }
  };

  const handleDeleteGame = async (gameId: string, gameName: string) => {
    if (!window.confirm(`Are you sure you want to permanently delete the game "${gameName}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(`/api/games/${gameId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete game');
      }

      toast({
        title: "Game Deleted",
        description: "The game has been permanently deleted.",
      });

      // Refresh the live games list
      loadLiveGames();
    } catch (error) {
      console.error('Error deleting game:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete game",
        variant: "destructive",
      });
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const now = new Date();
    const gameTime = new Date(dateString);
    const diffInSeconds = Math.floor((now.getTime() - gameTime.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return `${diffInSeconds}s ago`;
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}m ago`;
    } else {
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    }
  };

  if (isLoading) {
    return (
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Live Games
          </CardTitle>
          <CardDescription>
            Games currently in progress
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading live games...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-lg">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Live Games
              <Badge variant="default" className="ml-2">
                {liveGames.length}
              </Badge>
            </CardTitle>
            <CardDescription>
              Games currently in progress
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={loadLiveGames}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {error && (
          <div className="text-center py-4 text-red-600">
            <p>{error}</p>
            <Button variant="outline" size="sm" onClick={loadLiveGames} className="mt-2">
              Try Again
            </Button>
          </div>
        )}
        
        {!error && liveGames.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Play className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium">No live games</p>
            <p className="text-sm">Start a new game to see it here</p>
          </div>
        )}

        {!error && liveGames.length > 0 && (
          <ScrollArea className="h-[400px]">
            <div className="space-y-3">
              {liveGames.map((game, index) => (
                <div key={game.id}>
                  <div className="flex items-center justify-between p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <Badge variant="default" className="animate-pulse">
                          <Activity className="h-3 w-3 mr-1" />
                          LIVE
                        </Badge>
                        {game.tournament && (
                          <Badge variant="outline">
                            <Trophy className="h-3 w-3 mr-1" />
                            {game.tournament.name}
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex items-center gap-2 mb-2">
                        <span className="font-medium text-sm truncate">
                          {game.white}
                        </span>
                        <span className="text-muted-foreground text-xs">vs</span>
                        <span className="font-medium text-sm truncate">
                          {game.black}
                        </span>
                      </div>
                      
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {gameService.formatDuration(game.duration)}
                        </div>
                        <div className="flex items-center gap-1">
                          <Target className="h-3 w-3" />
                          {game.moveCount} moves
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {formatTimeAgo(game.createdAt)}
                        </div>
                      </div>
                      
                      {(game.whiteProfile || game.blackProfile) && (
                        <div className="flex items-center gap-2 mt-1 text-xs text-muted-foreground">
                          {game.whiteProfile && (
                            <span>White: {game.whiteProfile.eloRating} ELO</span>
                          )}
                          {game.blackProfile && (
                            <span>Black: {game.blackProfile.eloRating} ELO</span>
                          )}
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2 ml-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSpectate(game.id)}
                        className="flex items-center gap-1"
                      >
                        <Eye className="h-3 w-3" />
                        Watch
                      </Button>
                      
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => handlePauseGame(game.id, `${game.white} vs ${game.black}`)}
                            className="text-orange-600"
                          >
                            <Pause className="h-4 w-4 mr-2" />
                            Stop Game
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDeleteGame(game.id, `${game.white} vs ${game.black}`)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete Game
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                  
                  {index < liveGames.length - 1 && <Separator className="my-2" />}
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  );
}
