"use client";

import React, { useState, useEffect } from 'react';
import { SidebarNavigation, ViewType } from './sidebar-navigation';
import { ProfileManager } from './profile-manager';
import { TournamentManager } from './tournament-manager';
import { HistoryManager } from './history-manager';
import { GameActivitySidebar } from './game-activity-sidebar';
import { LiveGamesDashboard } from './live-games-dashboard';
import ChessDuelArena from './chess-duel-arena';
import { profileService, LLMProfile } from '@/lib/profile-client';

export function MainLayout() {
  const [currentView, setCurrentView] = useState<ViewType>('game');
  const [profiles, setProfiles] = useState<LLMProfile[]>([]);

  useEffect(() => {
    const fetchProfiles = async () => {
      try {
        const profilesData = await profileService.getAllProfiles();
        setProfiles(profilesData);
      } catch (error) {
        console.error('Error fetching profiles:', error);
      }
    };

    fetchProfiles();
  }, []);

  const renderContent = () => {
    switch (currentView) {
      case 'game':
        return (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <div className="lg:col-span-3">
              <ChessDuelArena />
            </div>
            <div className="lg:col-span-1 space-y-4">
              <LiveGamesDashboard />
              <GameActivitySidebar />
            </div>
          </div>
        );
      case 'profiles':
        return <ProfileManager />;
      case 'tournaments':
        return <TournamentManager profiles={profiles} />;
      case 'history':
        return <HistoryManager />;
      default:
        return <ChessDuelArena />;
    }
  };

  return (
    <SidebarNavigation
      currentView={currentView}
      onViewChange={setCurrentView}
    >
      {renderContent()}
    </SidebarNavigation>
  );
}

export default MainLayout;
