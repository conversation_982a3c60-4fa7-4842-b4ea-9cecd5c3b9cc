"use client";

import React, { useState, useEffect } from 'react';
import { SidebarNavigation, ViewType } from './sidebar-navigation';
import { ProfileManager } from './profile-manager';
import { TournamentManager } from './tournament-manager';
import { HistoryManager } from './history-manager';
import { GameActivitySidebar } from './game-activity-sidebar';
import ChessDuelArena from './chess-duel-arena';
import { profileService, LLMProfile } from '@/lib/profile-client';

export function MainLayout() {
  const [currentView, setCurrentView] = useState<ViewType>('game');
  const [profiles, setProfiles] = useState<LLMProfile[]>([]);

  useEffect(() => {
    const fetchProfiles = async () => {
      try {
        const profilesData = await profileService.getAllProfiles();
        setProfiles(profilesData);
      } catch (error) {
        console.error('Error fetching profiles:', error);
      }
    };

    fetchProfiles();
  }, []);

  const renderContent = () => {
    switch (currentView) {
      case 'game':
        return <ChessDuelArena />;
      case 'profiles':
        return <ProfileManager />;
      case 'tournaments':
        return <TournamentManager profiles={profiles} />;
      case 'history':
        return <HistoryManager />;
      default:
        return <ChessDuelArena />;
    }
  };

  return (
    <SidebarNavigation
      currentView={currentView}
      onViewChange={setCurrentView}
    >
      {renderContent()}
    </SidebarNavigation>
  );
}

export default MainLayout;
