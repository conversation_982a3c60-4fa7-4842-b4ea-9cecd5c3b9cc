"use client";

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { ProfileWithStats, getProfileVolatilityStatus } from '@/lib/profile-service';
import { ChevronDown, ChevronUp, Edit, Power, PowerOff, Trophy, Target, TrendingUp, Calendar } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ProfileCardProps {
  profile: ProfileWithStats;
  onEdit: (profile: ProfileWithStats) => void;
  onToggleActive: (id: string, isActive: boolean) => void;
}

export function ProfileCard({ profile, onEdit, onToggleActive }: ProfileCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const volatilityStatus = getProfileVolatilityStatus(profile.gamesPlayed);
  
  const getVolatilityColor = (status: string) => {
    switch (status) {
      case 'volatile': return 'bg-red-100 text-red-800 border-red-200';
      case 'stabilizing': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'stable': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getResultColor = (result: 'win' | 'loss' | 'draw') => {
    switch (result) {
      case 'win': return 'text-green-600';
      case 'loss': return 'text-red-600';
      case 'draw': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  const getResultIcon = (result: 'win' | 'loss' | 'draw') => {
    switch (result) {
      case 'win': return '✓';
      case 'loss': return '✗';
      case 'draw': return '=';
      default: return '-';
    }
  };

  const formatEloChange = (change: number) => {
    const sign = change >= 0 ? '+' : '';
    return `${sign}${change}`;
  };

  const getModelDisplayName = (model: string) => {
    if (model.startsWith('openrouter/')) {
      const parts = model.split('/');
      return parts[parts.length - 1].replace(':free', '');
    }
    return model;
  };

  return (
    <Card className={cn(
      "transition-all duration-200 hover:shadow-md",
      !profile.isActive && "opacity-60 bg-gray-50"
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <Avatar className="h-10 w-10">
              <AvatarFallback className="bg-blue-100 text-blue-700 font-semibold">
                {profile.name.substring(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div>
              <CardTitle className="text-lg font-semibold">{profile.name}</CardTitle>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <CardDescription className="text-sm cursor-help">
                      {getModelDisplayName(profile.model)}
                    </CardDescription>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="font-mono text-xs">{profile.model}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Badge className={getVolatilityColor(volatilityStatus)}>
              {volatilityStatus}
            </Badge>
            {!profile.isActive && (
              <Badge variant="secondary" className="bg-gray-200 text-gray-600">
                Inactive
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* ELO Rating */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Trophy className="h-4 w-4 text-yellow-600" />
            <span className="font-medium">ELO Rating</span>
          </div>
          <span className="text-2xl font-bold text-blue-600">{profile.eloRating}</span>
        </div>

        {/* Statistics Grid */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold">{profile.gamesPlayed}</div>
            <div className="text-sm text-gray-600">Games</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">
              {profile.gamesPlayed > 0 ? profile.winRate.toFixed(1) : '0.0'}%
            </div>
            <div className="text-sm text-gray-600">Win Rate</div>
          </div>
        </div>

        {/* Win/Loss/Draw Stats */}
        <div className="grid grid-cols-3 gap-2 text-center">
          <div className="p-2 bg-green-50 rounded">
            <div className="text-lg font-semibold text-green-600">{profile.wins}</div>
            <div className="text-xs text-green-600">Wins</div>
          </div>
          <div className="p-2 bg-yellow-50 rounded">
            <div className="text-lg font-semibold text-yellow-600">{profile.draws}</div>
            <div className="text-xs text-yellow-600">Draws</div>
          </div>
          <div className="p-2 bg-red-50 rounded">
            <div className="text-lg font-semibold text-red-600">{profile.losses}</div>
            <div className="text-xs text-red-600">Losses</div>
          </div>
        </div>

        {/* Average Opponent ELO */}
        {profile.gamesPlayed > 0 && (
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Target className="h-4 w-4 text-gray-600" />
              <span className="text-sm text-gray-600">Avg Opponent ELO</span>
            </div>
            <span className="font-semibold">{profile.averageOpponentElo}</span>
          </div>
        )}

        {/* Recent Matches Collapsible */}
        {profile.recentMatches.length > 0 && (
          <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="w-full justify-between p-2">
                <span className="flex items-center space-x-2">
                  <TrendingUp className="h-4 w-4" />
                  <span>Recent Matches ({profile.recentMatches.length})</span>
                </span>
                {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-2 mt-2">
              {profile.recentMatches.slice(0, 5).map((match) => (
                <div key={match.id} className="flex items-center justify-between p-2 bg-gray-50 rounded text-sm">
                  <div className="flex items-center space-x-2">
                    <span className={cn("font-mono text-xs", getResultColor(match.result))}>
                      {getResultIcon(match.result)}
                    </span>
                    <span className="font-medium">{getModelDisplayName(match.opponent.model)}</span>
                    <Badge variant="outline" className="text-xs">
                      {match.playedAs}
                    </Badge>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={cn("font-semibold text-xs", 
                      match.eloChange >= 0 ? "text-green-600" : "text-red-600"
                    )}>
                      {formatEloChange(match.eloChange)}
                    </span>
                    <span className="text-xs text-gray-500">
                      {new Date(match.completedAt).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              ))}
              {profile.recentMatches.length > 5 && (
                <div className="text-center text-xs text-gray-500 pt-1">
                  ... and {profile.recentMatches.length - 5} more matches
                </div>
              )}
            </CollapsibleContent>
          </Collapsible>
        )}

        {/* Action Buttons */}
        <div className="flex space-x-2 pt-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onEdit(profile)}
            className="flex-1"
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button
            variant={profile.isActive ? "destructive" : "default"}
            size="sm"
            onClick={() => onToggleActive(profile.id, !profile.isActive)}
            className="flex-1"
          >
            {profile.isActive ? (
              <>
                <PowerOff className="h-4 w-4 mr-2" />
                Deactivate
              </>
            ) : (
              <>
                <Power className="h-4 w-4 mr-2" />
                Activate
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}