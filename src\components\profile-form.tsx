"use client";

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ProfileWithStats } from '@/lib/profile-service';
import { AlertCircle, Loader2 } from 'lucide-react';

type Model = 
  | 'gemini-2.0-flash' 
  | 'gemini-2.5-flash' 
  | 'gemini-2.5-pro' 
  | 'openrouter/openai/gpt-oss-20b:free'
  | 'openrouter/z-ai/glm-4.5-air:free'
  | 'openrouter/moonshotai/kimi-k2:free'
  | 'openrouter/google/gemma-3n-e2b-it:free'
  | 'openrouter/deepseek/deepseek-r1-0528:free'
  | 'openrouter/qwen/qwen3-235b-a22b:free';

const ALL_MODELS: Model[] = [
  // Google AI (Direct - Genkit only)
  'gemini-2.0-flash', 
  'gemini-2.5-flash', 
  'gemini-2.5-pro', 
  
  // OpenRouter - Free Models
  'openrouter/openai/gpt-oss-20b:free',
  'openrouter/z-ai/glm-4.5-air:free',
  'openrouter/moonshotai/kimi-k2:free',
  'openrouter/google/gemma-3n-e2b-it:free',
  'openrouter/deepseek/deepseek-r1-0528:free',
  'openrouter/qwen/qwen3-235b-a22b:free'
];

interface ProfileFormData {
  name: string;
  model: Model | '';
}

interface ProfileFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: ProfileFormData) => Promise<void>;
  profile?: ProfileWithStats | null;
  title?: string;
  description?: string;
}

export function ProfileForm({ 
  isOpen, 
  onClose, 
  onSubmit, 
  profile = null,
  title,
  description 
}: ProfileFormProps) {
  const [formData, setFormData] = useState<ProfileFormData>({
    name: '',
    model: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const isEditing = !!profile;
  const defaultTitle = isEditing ? 'Edit Profile' : 'Create New Profile';
  const defaultDescription = isEditing 
    ? 'Update the profile information below.' 
    : 'Create a new LLM profile to participate in tournaments.';

  // Reset form when dialog opens/closes or profile changes
  useEffect(() => {
    if (isOpen) {
      setFormData({
        name: profile?.name || '',
        model: (profile?.model as Model) || '',
      });
      setError(null);
    }
  }, [isOpen, profile]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      setError('Profile name is required');
      return;
    }
    
    if (!formData.model) {
      setError('Model selection is required');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      await onSubmit({
        name: formData.name.trim(),
        model: formData.model,
      });
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  const getModelDisplayName = (model: string) => {
    if (model.startsWith('openrouter/')) {
      const parts = model.split('/');
      const name = parts[parts.length - 1].replace(':free', '');
      const provider = parts[1];
      return `${name} (${provider})`;
    }
    return model;
  };

  const getModelGroup = (model: string) => {
    if (model.startsWith('gemini-')) return 'Google AI';
    if (model.startsWith('openrouter/')) return 'OpenRouter';
    return 'Other';
  };

  // Group models by provider
  const groupedModels = ALL_MODELS.reduce((groups, model) => {
    const group = getModelGroup(model);
    if (!groups[group]) groups[group] = [];
    groups[group].push(model);
    return groups;
  }, {} as Record<string, Model[]>);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{title || defaultTitle}</DialogTitle>
          <DialogDescription>
            {description || defaultDescription}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="name">Profile Name</Label>
            <Input
              id="name"
              placeholder="e.g., GPT-4 Turbo, Claude 3.5 Sonnet"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              disabled={isSubmitting}
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="model">Model</Label>
            <Select
              value={formData.model}
              onValueChange={(value: Model) => setFormData(prev => ({ ...prev, model: value }))}
              disabled={isSubmitting}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a model" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(groupedModels).map(([group, models]) => (
                  <div key={group}>
                    <div className="px-2 py-1.5 text-sm font-semibold text-gray-900 bg-gray-100">
                      {group}
                    </div>
                    {models.map((model) => (
                      <SelectItem key={model} value={model}>
                        <div className="flex flex-col">
                          <span>{getModelDisplayName(model)}</span>
                          <span className="text-xs text-gray-500 font-mono">{model}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </div>
                ))}
              </SelectContent>
            </Select>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isEditing ? 'Update Profile' : 'Create Profile'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}