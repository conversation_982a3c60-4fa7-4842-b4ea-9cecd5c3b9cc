"use client";

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { useToast } from '@/hooks/use-toast';
import { ProfileCard } from './profile-card';
import { ProfileForm } from './profile-form';
import { RecentMatchesList } from './recent-matches-list';
import { profileService, LLMProfile } from '@/lib/profile-client';

interface ProfileWithStats extends LLMProfile {
  winRate: number;
  recentMatches?: any[];
}
import { Plus, Search, Filter, Users, Trophy, AlertCircle, Loader2 } from 'lucide-react';

type SortOption = 'elo-desc' | 'elo-asc' | 'name-asc' | 'name-desc' | 'games-desc' | 'winrate-desc';
type FilterOption = 'all' | 'active' | 'inactive' | 'volatile' | 'stable';

interface ProfileManagerProps {
  className?: string;
}

export function ProfileManager({ className }: ProfileManagerProps) {
  const [profiles, setProfiles] = useState<ProfileWithStats[]>([]);
  const [filteredProfiles, setFilteredProfiles] = useState<ProfileWithStats[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Form state
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingProfile, setEditingProfile] = useState<ProfileWithStats | null>(null);
  
  // Delete confirmation state
  const [deletingProfile, setDeletingProfile] = useState<ProfileWithStats | null>(null);
  
  // Filter and search state
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<SortOption>('elo-desc');
  const [filterBy, setFilterBy] = useState<FilterOption>('all');
  
  const { toast } = useToast();

  // Load profiles on component mount
  useEffect(() => {
    loadProfiles();
  }, []);

  // Apply filters and sorting when profiles or filter options change
  useEffect(() => {
    applyFiltersAndSort();
  }, [profiles, searchTerm, sortBy, filterBy]);

  const loadProfiles = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const allProfiles = await profileService.getAllProfiles();
      
      // Load detailed stats for each profile
      const profilesWithStats = await Promise.all(
        allProfiles.map(async (profile) => {
          try {
            const detailedProfile = await profileService.getProfileStats(profile.id);

            return detailedProfile || {
              ...profile,
              winRate: 0,
              averageOpponentElo: 0,
              recentMatches: [],
            };
          } catch (err) {
            console.error(`Failed to load stats for profile ${profile.id}:`, err);
            return {
              ...profile,
              winRate: 0,
              averageOpponentElo: 0,
              recentMatches: [],
            };
          }
        })
      );
      
      setProfiles(profilesWithStats);
    } catch (err) {
      console.error('Failed to load profiles:', err);
      setError('Failed to load profiles. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const applyFiltersAndSort = () => {
    let filtered = [...profiles];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(profile =>
        profile.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        profile.model.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    switch (filterBy) {
      case 'active':
        filtered = filtered.filter(p => p.isActive);
        break;
      case 'inactive':
        filtered = filtered.filter(p => !p.isActive);
        break;
      case 'volatile':
        filtered = filtered.filter(p => p.gamesPlayed < 5);
        break;
      case 'stable':
        filtered = filtered.filter(p => p.gamesPlayed >= 10);
        break;
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'elo-desc':
          return b.eloRating - a.eloRating;
        case 'elo-asc':
          return a.eloRating - b.eloRating;
        case 'name-asc':
          return a.name.localeCompare(b.name);
        case 'name-desc':
          return b.name.localeCompare(a.name);
        case 'games-desc':
          return b.gamesPlayed - a.gamesPlayed;
        case 'winrate-desc':
          return b.winRate - a.winRate;
        default:
          return 0;
      }
    });

    setFilteredProfiles(filtered);
  };

  const handleCreateProfile = async (data: { name: string; model: string }) => {
    try {
      await profileService.createProfile(data);
      toast({
        title: "Profile Created",
        description: `${data.name} has been created successfully.`,
      });
      await loadProfiles();
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Failed to create profile');
    }
  };

  const handleUpdateProfile = async (data: { name: string; model: string }) => {
    if (!editingProfile) return;

    try {
      await profileService.updateProfile(editingProfile.id, data);
      toast({
        title: "Profile Updated",
        description: `${data.name} has been updated successfully.`,
      });
      await loadProfiles();
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Failed to update profile');
    }
  };

  const handleToggleActive = async (id: string, isActive: boolean) => {
    try {
      await profileService.updateProfile(id, { isActive });
      toast({
        title: isActive ? "Profile Activated" : "Profile Deactivated",
        description: `Profile has been ${isActive ? 'activated' : 'deactivated'} successfully.`,
      });
      await loadProfiles();
    } catch (err) {
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : 'Failed to update profile',
        variant: "destructive",
      });
    }
  };

  const handleDeleteProfile = async () => {
    if (!deletingProfile) return;

    try {
      await profileService.deleteProfile(deletingProfile.id);
      toast({
        title: "Profile Deleted",
        description: `${deletingProfile.name} has been deleted successfully.`,
      });
      await loadProfiles();
    } catch (err) {
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : 'Failed to delete profile',
        variant: "destructive",
      });
    } finally {
      setDeletingProfile(null);
    }
  };

  const openCreateForm = () => {
    setEditingProfile(null);
    setIsFormOpen(true);
  };

  const openEditForm = (profile: ProfileWithStats) => {
    setEditingProfile(profile);
    setIsFormOpen(true);
  };

  const closeForm = () => {
    setIsFormOpen(false);
    setEditingProfile(null);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading profiles...</span>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">LLM Profiles</h1>
          <p className="text-gray-600">Manage AI model profiles and track their ELO ratings</p>
        </div>
        <Button onClick={openCreateForm}>
          <Plus className="h-4 w-4 mr-2" />
          Create Profile
        </Button>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Total Profiles</p>
                <p className="text-2xl font-bold">{profiles.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Trophy className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Active Profiles</p>
                <p className="text-2xl font-bold">{profiles.filter(p => p.isActive).length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div>
              <p className="text-sm text-gray-600">Highest ELO</p>
              <p className="text-2xl font-bold">
                {profiles.length > 0 ? Math.max(...profiles.map(p => p.eloRating)) : 0}
              </p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div>
              <p className="text-sm text-gray-600">Total Games</p>
              <p className="text-2xl font-bold">
                {profiles.reduce((sum, p) => sum + p.gamesPlayed, 0)}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search profiles..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <Select value={sortBy} onValueChange={(value: SortOption) => setSortBy(value)}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="elo-desc">ELO (High to Low)</SelectItem>
            <SelectItem value="elo-asc">ELO (Low to High)</SelectItem>
            <SelectItem value="name-asc">Name (A to Z)</SelectItem>
            <SelectItem value="name-desc">Name (Z to A)</SelectItem>
            <SelectItem value="games-desc">Most Games</SelectItem>
            <SelectItem value="winrate-desc">Highest Win Rate</SelectItem>
          </SelectContent>
        </Select>
        <Select value={filterBy} onValueChange={(value: FilterOption) => setFilterBy(value)}>
          <SelectTrigger className="w-[140px]">
            <SelectValue placeholder="Filter" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Profiles</SelectItem>
            <SelectItem value="active">Active Only</SelectItem>
            <SelectItem value="inactive">Inactive Only</SelectItem>
            <SelectItem value="volatile">Volatile (&lt; 5 games)</SelectItem>
            <SelectItem value="stable">Stable (10+ games)</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Profiles Grid */}
      {filteredProfiles.length === 0 ? (
        <Card>
          <CardContent className="py-12">
            <div className="text-center">
              <Users className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-semibold mb-2">No profiles found</h3>
              <p className="text-gray-600 mb-4">
                {profiles.length === 0 
                  ? "Create your first LLM profile to get started with tournaments."
                  : "Try adjusting your search or filter criteria."
                }
              </p>
              {profiles.length === 0 && (
                <Button onClick={openCreateForm}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Profile
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredProfiles.map((profile) => (
            <ProfileCard
              key={profile.id}
              profile={profile}
              onEdit={openEditForm}
              onToggleActive={handleToggleActive}
            />
          ))}
        </div>
      )}

      {/* Profile Form Dialog */}
      <ProfileForm
        isOpen={isFormOpen}
        onClose={closeForm}
        onSubmit={editingProfile ? handleUpdateProfile : handleCreateProfile}
        profile={editingProfile}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingProfile} onOpenChange={() => setDeletingProfile(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Profile</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete &quot;{deletingProfile?.name}&quot;? This action cannot be undone.
              {deletingProfile && deletingProfile.gamesPlayed > 0 && (
                <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded">
                  <p className="text-sm text-yellow-800">
                    This profile has played {deletingProfile.gamesPlayed} games. 
                    Consider deactivating instead of deleting to preserve match history.
                  </p>
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteProfile} className="bg-red-600 hover:bg-red-700">
              Delete Profile
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}