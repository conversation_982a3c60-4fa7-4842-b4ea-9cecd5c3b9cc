'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, Play, Pause, Trophy, Users, Calendar, Clock } from 'lucide-react';
import { TournamentForm } from './tournament-form';
import { TournamentBracket } from './tournament-bracket';
import { TournamentScheduler } from './tournament-scheduler';
import { tournamentService } from '@/lib/tournament-client';
import type { TournamentWithDetails as ServerTournamentWithDetails } from '@/lib/tournament-service';
import { Skeleton } from '@/components/ui/skeleton';

interface LLMProfile {
  id: string;
  name: string;
  model: string;
  eloRating: number;
  isActive: boolean;
}

interface TournamentManagerProps {
  profiles: LLMProfile[];
}

export function TournamentManager({ profiles }: TournamentManagerProps) {
  const [tournaments, setTournaments] = useState<ServerTournamentWithDetails[]>([]);
  const [selectedTournament, setSelectedTournament] = useState<ServerTournamentWithDetails | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showScheduler, setShowScheduler] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadTournaments();
  }, []);

  const loadTournaments = async () => {
    try {
      setLoading(true);
      const data = await tournamentService.getAllTournaments();
      // Cast API response to server type used by UI components
      setTournaments(data as unknown as ServerTournamentWithDetails[]);
    } catch (error) {
      console.error('Failed to load tournaments:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTournament = async (tournamentData: any) => {
    try {
      const newTournament = await tournamentService.createTournament(tournamentData);
      setTournaments(prev => [newTournament as unknown as ServerTournamentWithDetails, ...prev]);
      setShowCreateForm(false);
    } catch (error) {
      console.error('Failed to create tournament:', error);
    }
  };

  const handleStartTournament = async (tournamentId: string) => {
    try {
      await tournamentService.startTournament(tournamentId);
      await loadTournaments();
    } catch (error) {
      console.error('Failed to start tournament:', error);
    }
  };

  const handlePauseTournament = async (tournamentId: string) => {
    try {
      await tournamentService.pauseTournament(tournamentId);
      await loadTournaments();
    } catch (error) {
      console.error('Failed to pause tournament:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return 'bg-gray-500';
      case 'SCHEDULED':
        return 'bg-blue-500';
      case 'ACTIVE':
        return 'bg-green-500';
      case 'PAUSED':
        return 'bg-yellow-500';
      case 'COMPLETED':
        return 'bg-purple-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getFormatDisplay = (format: string) => {
    switch (format) {
      case 'ROUND_ROBIN':
        return 'Round Robin';
      case 'SINGLE_ELIMINATION':
        return 'Single Elimination';
      case 'DOUBLE_ELIMINATION':
        return 'Double Elimination';
      default:
        return format;
    }
  };

  const formatTimeWindow = (timeWindow: string) => {
    const [start, end] = timeWindow.split('-');
    return `${start} - ${end}`;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <div className="h-7 w-48 rounded bg-muted" />
            <div className="h-4 w-72 rounded bg-muted mt-2" />
          </div>
          <Skeleton className="h-9 w-40" />
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Skeleton className="h-5 w-5 rounded-full" />
                    <div>
                      <Skeleton className="h-5 w-40" />
                      <Skeleton className="h-3 w-60 mt-2" />
                    </div>
                  </div>
                  <Skeleton className="h-6 w-24" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  {Array.from({ length: 4 }).map((_, j) => (
                    <div key={j} className="flex items-center space-x-2">
                      <Skeleton className="h-4 w-4" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (selectedTournament) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={() => setSelectedTournament(null)}
            >
              ← Back to Tournaments
            </Button>
            <div>
              <h2 className="text-2xl font-bold">{selectedTournament.name}</h2>
              <div className="flex items-center space-x-2 mt-1">
                <Badge className={getStatusColor(selectedTournament.status)}>
                  {selectedTournament.status}
                </Badge>
                <span className="text-sm text-muted-foreground">
                  {getFormatDisplay(selectedTournament.format)}
                </span>
              </div>
            </div>
          </div>
          <div className="flex space-x-2">
            {selectedTournament.status === 'DRAFT' && (
              <Button
                onClick={() => handleStartTournament(selectedTournament.id)}
                className="bg-green-600 hover:bg-green-700"
              >
                <Play className="w-4 h-4 mr-2" />
                Start Tournament
              </Button>
            )}
            {selectedTournament.status === 'ACTIVE' && (
              <Button
                onClick={() => handlePauseTournament(selectedTournament.id)}
                variant="outline"
              >
                <Pause className="w-4 h-4 mr-2" />
                Pause
              </Button>
            )}
            {(selectedTournament.status === 'DRAFT' || 
              selectedTournament.status === 'SCHEDULED') && (
              <Button
                onClick={() => setShowScheduler(true)}
                variant="outline"
              >
                <Calendar className="w-4 h-4 mr-2" />
                Schedule
              </Button>
            )}
          </div>
        </div>

        <TournamentBracket tournament={selectedTournament} />

        {showScheduler && (
          <TournamentScheduler
            tournament={selectedTournament}
            onClose={() => setShowScheduler(false)}
            onSchedule={loadTournaments}
          />
        )}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Tournaments</h2>
          <p className="text-muted-foreground">
            Manage and monitor LLM chess tournaments
          </p>
        </div>
        <Button
          onClick={() => setShowCreateForm(true)}
          className="bg-blue-600 hover:bg-blue-700"
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Tournament
        </Button>
      </div>

      {tournaments.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Trophy className="w-12 h-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No tournaments yet</h3>
            <p className="text-muted-foreground text-center mb-4">
              Create your first tournament to start competing LLM models against each other.
            </p>
            <Button
              onClick={() => setShowCreateForm(true)}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create Tournament
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {tournaments.map((tournament) => (
            <Card
              key={tournament.id}
              className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => setSelectedTournament(tournament)}
            >
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Trophy className="w-5 h-5 text-yellow-600" />
                    <div>
                      <CardTitle className="text-lg">{tournament.name}</CardTitle>
                      <CardDescription>
                        {getFormatDisplay(tournament.format)} • {tournament.participants.length} participants
                      </CardDescription>
                    </div>
                  </div>
                  <Badge className={getStatusColor(tournament.status)}>
                    {tournament.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div className="flex items-center space-x-2">
                    <Users className="w-4 h-4 text-muted-foreground" />
                    <span>{tournament.participants.length} players</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Trophy className="w-4 h-4 text-muted-foreground" />
                    <span>{tournament.matches.filter(m => m.status === 'COMPLETED').length}/{tournament.matches.length} matches</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-muted-foreground" />
                    <span>{tournament.matchInterval}min intervals</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4 text-muted-foreground" />
                    <span>{tournament.timeWindow ? formatTimeWindow(tournament.timeWindow) : '—'}</span>
                  </div>
                </div>
                
                {tournament.participants.length > 0 && (
                  <div className="mt-4">
                    <div className="text-xs text-muted-foreground mb-2">Participants:</div>
                    <div className="flex flex-wrap gap-1">
                      {tournament.participants.slice(0, 6).map((participant) => (
                        <Badge key={participant.id} variant="outline" className="text-xs">
                          {participant.profile.name}
                        </Badge>
                      ))}
                      {tournament.participants.length > 6 && (
                        <Badge variant="outline" className="text-xs">
                          +{tournament.participants.length - 6} more
                        </Badge>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {showCreateForm && (
        <TournamentForm
          profiles={profiles.filter(p => p.isActive !== false)}
          onSubmit={handleCreateTournament}
          onCancel={() => setShowCreateForm(false)}
        />
      )}
    </div>
  );
}