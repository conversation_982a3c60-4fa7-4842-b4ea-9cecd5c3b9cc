'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { X, Calendar, Clock, Play, Pause, Settings } from 'lucide-react';
import { TournamentWithDetails, TournamentService } from '@/lib/tournament-service';
import { TournamentStatus } from '@prisma/client';

interface TournamentSchedulerProps {
  tournament: TournamentWithDetails;
  onClose: () => void;
  onSchedule: () => void;
}

export function TournamentScheduler({ tournament, onClose, onSchedule }: TournamentSchedulerProps) {
  const [schedulerData, setSchedulerData] = useState({
    scheduledStart: tournament.scheduledStart 
      ? new Date(tournament.scheduledStart).toISOString().slice(0, 16)
      : '',
    matchInterval: tournament.matchInterval,
    timeWindow: tournament.timeWindow,
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const newErrors: { [key: string]: string } = {};

    if (!schedulerData.scheduledStart) {
      newErrors.scheduledStart = 'Start time is required';
    }

    if (schedulerData.matchInterval < 1 || schedulerData.matchInterval > 1440) {
      newErrors.matchInterval = 'Match interval must be between 1 and 1440 minutes';
    }

    if (!isValidTimeWindow(schedulerData.timeWindow)) {
      newErrors.timeWindow = 'Invalid time window format (use HH:MM-HH:MM)';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    try {
      setLoading(true);
      
      // Update tournament with new scheduling settings
      await TournamentService.updateTournamentStatus(tournament.id, TournamentStatus.SCHEDULED);
      
      // In a real implementation, you would also update the scheduling fields
      // For now, we'll just update the status
      
      onSchedule();
      onClose();
    } catch (error) {
      console.error('Failed to schedule tournament:', error);
    } finally {
      setLoading(false);
    }
  };

  const isValidTimeWindow = (timeWindow: string): boolean => {
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]-([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
    return timeRegex.test(timeWindow);
  };

  const getEstimatedDuration = () => {
    const totalMatches = tournament.matches.length;
    const totalMinutes = totalMatches * schedulerData.matchInterval;
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    
    if (hours > 0) {
      return `~${hours}h ${minutes}m`;
    }
    return `~${minutes}m`;
  };

  const getEstimatedEndTime = () => {
    if (!schedulerData.scheduledStart) return 'N/A';
    
    const startTime = new Date(schedulerData.scheduledStart);
    const totalMinutes = tournament.matches.length * schedulerData.matchInterval;
    const endTime = new Date(startTime.getTime() + totalMinutes * 60000);
    
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(endTime);
  };

  const parseTimeWindow = (timeWindow: string) => {
    const [start, end] = timeWindow.split('-');
    return { start, end };
  };

  const formatTimeWindow = (timeWindow: string) => {
    const { start, end } = parseTimeWindow(timeWindow);
    return `${start} - ${end}`;
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Calendar className="w-5 h-5" />
                <span>Schedule Tournament</span>
              </CardTitle>
              <CardDescription>
                Configure automated scheduling for {tournament.name}
              </CardDescription>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Tournament Info */}
            <Card className="bg-muted/50">
              <CardContent className="pt-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Format:</span>
                    <div className="font-medium">{tournament.format.replace('_', ' ')}</div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Participants:</span>
                    <div className="font-medium">{tournament.participants.length}</div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Total Matches:</span>
                    <div className="font-medium">{tournament.matches.length}</div>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Status:</span>
                    <Badge className="ml-2">{tournament.status}</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Start Time */}
            <div className="space-y-2">
              <Label htmlFor="scheduledStart">Tournament Start Time</Label>
              <Input
                id="scheduledStart"
                type="datetime-local"
                value={schedulerData.scheduledStart}
                onChange={(e) => setSchedulerData(prev => ({ 
                  ...prev, 
                  scheduledStart: e.target.value 
                }))}
                className={errors.scheduledStart ? 'border-red-500' : ''}
              />
              {errors.scheduledStart && (
                <p className="text-sm text-red-500">{errors.scheduledStart}</p>
              )}
              <p className="text-sm text-muted-foreground">
                The tournament will start automatically at this time
              </p>
            </div>

            {/* Match Interval */}
            <div className="space-y-2">
              <Label htmlFor="matchInterval">Match Interval (minutes)</Label>
              <Input
                id="matchInterval"
                type="number"
                min="1"
                max="1440"
                value={schedulerData.matchInterval}
                onChange={(e) => setSchedulerData(prev => ({ 
                  ...prev, 
                  matchInterval: parseInt(e.target.value) || 30 
                }))}
                className={errors.matchInterval ? 'border-red-500' : ''}
              />
              {errors.matchInterval && (
                <p className="text-sm text-red-500">{errors.matchInterval}</p>
              )}
              <p className="text-sm text-muted-foreground">
                Time between consecutive matches
              </p>
            </div>

            {/* Daily Time Window */}
            <div className="space-y-2">
              <Label htmlFor="timeWindow">Daily Time Window</Label>
              <Input
                id="timeWindow"
                value={schedulerData.timeWindow}
                onChange={(e) => setSchedulerData(prev => ({ 
                  ...prev, 
                  timeWindow: e.target.value 
                }))}
                placeholder="09:00-17:00"
                className={errors.timeWindow ? 'border-red-500' : ''}
              />
              {errors.timeWindow && (
                <p className="text-sm text-red-500">{errors.timeWindow}</p>
              )}
              <p className="text-sm text-muted-foreground">
                Matches will only be scheduled within this daily time window
              </p>
            </div>

            {/* Schedule Preview */}
            <Card className="bg-blue-50 dark:bg-blue-950/20">
              <CardHeader>
                <CardTitle className="text-lg flex items-center space-x-2">
                  <Settings className="w-5 h-5" />
                  <span>Schedule Preview</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center space-x-2">
                    <Play className="w-4 h-4 text-green-600" />
                    <span>Start: {schedulerData.scheduledStart ? 
                      new Intl.DateTimeFormat('en-US', {
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                      }).format(new Date(schedulerData.scheduledStart)) : 'Not set'
                    }</span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Pause className="w-4 h-4 text-orange-600" />
                    <span>End: {getEstimatedEndTime()}</span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-blue-600" />
                    <span>Duration: {getEstimatedDuration()}</span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-4 h-4 text-purple-600" />
                    <span>Window: {formatTimeWindow(schedulerData.timeWindow)}</span>
                  </div>
                </div>

                <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-950/20 rounded-md">
                  <p className="text-sm text-yellow-800 dark:text-yellow-200">
                    <strong>Note:</strong> Matches will be scheduled automatically based on participant availability. 
                    If participants are busy, matches will be queued and executed when they become available.
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Upcoming Matches Preview */}
            {tournament.matches.filter(m => m.status === 'SCHEDULED').length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Next Matches</CardTitle>
                  <CardDescription>
                    First few matches that will be scheduled
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {tournament.matches
                      .filter(m => m.status === 'SCHEDULED')
                      .slice(0, 5)
                      .map((match, index) => (
                        <div key={match.id} className="flex items-center justify-between p-2 bg-muted/50 rounded">
                          <div className="flex items-center space-x-3">
                            <Badge variant="outline" className="w-6 h-6 rounded-full flex items-center justify-center text-xs">
                              {index + 1}
                            </Badge>
                            <span className="text-sm">
                              {match.whiteProfile.name} vs {match.blackProfile.name}
                            </span>
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {match.round}
                          </Badge>
                        </div>
                      ))}
                    {tournament.matches.filter(m => m.status === 'SCHEDULED').length > 5 && (
                      <div className="text-center text-sm text-muted-foreground">
                        +{tournament.matches.filter(m => m.status === 'SCHEDULED').length - 5} more matches
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Form Actions */}
            <div className="flex justify-end space-x-3 pt-4">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button 
                type="submit" 
                className="bg-green-600 hover:bg-green-700"
                disabled={loading}
              >
                {loading ? 'Scheduling...' : 'Schedule Tournament'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}