import { describe, it, expect, beforeEach, afterEach, vi, Mock } from 'vitest';
import { BackgroundJobSystem, JobStatus } from '../background-jobs';
import { TournamentStatus } from '@prisma/client';

// Mock dependencies
vi.mock('../db', async (importOriginal) => {
  const actual = await importOriginal()
  return {
    ...actual,
    db: {
      tournament: {
        findMany: vi.fn(),
        findUnique: vi.fn(),
        update: vi.fn(),
      },
    },
  }
});

vi.mock('../match-scheduler', () => ({
  MatchScheduler: {
    startTournamentScheduler: vi.fn(),
    stopTournamentScheduler: vi.fn(),
    getRunningMatches: vi.fn().mockReturnValue([]),
  },
}));

import { db } from '../db';
import { MatchScheduler } from '../match-scheduler';

describe('BackgroundJobSystem', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Clear job state
    (BackgroundJobSystem as any).jobs.clear();
    
    // Stop any running intervals
    if ((BackgroundJobSystem as any).healthCheckInterval) {
      clearInterval((BackgroundJobSystem as any).healthCheckInterval);
      (BackgroundJobSystem as any).healthCheckInterval = null;
    }
  });

  afterEach(() => {
    BackgroundJobSystem.shutdown();
  });

  describe('startTournamentJob', () => {
    it('should start job for active tournament', async () => {
      const mockTournament = {
        status: TournamentStatus.ACTIVE,
        name: 'Test Tournament',
      };

      (db.tournament.findUnique as Mock).mockResolvedValue(mockTournament);
      (MatchScheduler.startTournamentScheduler as Mock).mockResolvedValue(undefined);

      await BackgroundJobSystem.startTournamentJob('test-tournament');

      const jobStatus = BackgroundJobSystem.getJobStatus('test-tournament');
      expect(jobStatus).toBeTruthy();
      expect(jobStatus?.status).toBe('running');
      expect(jobStatus?.type).toBe('tournament-scheduler');

      expect(MatchScheduler.startTournamentScheduler).toHaveBeenCalledWith('test-tournament');
    });

    it('should throw error for non-existent tournament', async () => {
      (db.tournament.findUnique as Mock).mockResolvedValue(null);

      await expect(BackgroundJobSystem.startTournamentJob('test-tournament'))
        .rejects.toThrow('Tournament not found');

      const jobStatus = BackgroundJobSystem.getJobStatus('test-tournament');
      expect(jobStatus?.status).toBe('error');
    });

    it('should throw error for inactive tournament', async () => {
      const mockTournament = {
        status: TournamentStatus.DRAFT,
        name: 'Test Tournament',
      };

      (db.tournament.findUnique as Mock).mockResolvedValue(mockTournament);

      await expect(BackgroundJobSystem.startTournamentJob('test-tournament'))
        .rejects.toThrow('Tournament is not active');

      const jobStatus = BackgroundJobSystem.getJobStatus('test-tournament');
      expect(jobStatus?.status).toBe('error');
    });

    it('should stop existing job before starting new one', async () => {
      const mockTournament = {
        status: TournamentStatus.ACTIVE,
        name: 'Test Tournament',
      };

      (db.tournament.findUnique as Mock).mockResolvedValue(mockTournament);
      (MatchScheduler.startTournamentScheduler as Mock).mockResolvedValue(undefined);

      // Start first job
      await BackgroundJobSystem.startTournamentJob('test-tournament');
      
      // Start second job (should stop first)
      await BackgroundJobSystem.startTournamentJob('test-tournament');

      expect(MatchScheduler.stopTournamentScheduler).toHaveBeenCalledWith('test-tournament');
      expect(MatchScheduler.startTournamentScheduler).toHaveBeenCalledTimes(2);
    });
  });

  describe('stopTournamentJob', () => {
    it('should stop running job', async () => {
      const mockTournament = {
        status: TournamentStatus.ACTIVE,
        name: 'Test Tournament',
      };

      (db.tournament.findUnique as Mock).mockResolvedValue(mockTournament);
      (MatchScheduler.startTournamentScheduler as Mock).mockResolvedValue(undefined);

      // Start job
      await BackgroundJobSystem.startTournamentJob('test-tournament');
      
      // Stop job
      BackgroundJobSystem.stopTournamentJob('test-tournament');

      const jobStatus = BackgroundJobSystem.getJobStatus('test-tournament');
      expect(jobStatus?.status).toBe('stopped');
      expect(MatchScheduler.stopTournamentScheduler).toHaveBeenCalledWith('test-tournament');
    });

    it('should handle stopping non-existent job gracefully', () => {
      expect(() => BackgroundJobSystem.stopTournamentJob('non-existent'))
        .not.toThrow();
    });
  });

  describe('getAllJobStatuses', () => {
    it('should return all job statuses', async () => {
      const mockTournament = {
        status: TournamentStatus.ACTIVE,
        name: 'Test Tournament',
      };

      (db.tournament.findUnique as Mock).mockResolvedValue(mockTournament);
      (MatchScheduler.startTournamentScheduler as Mock).mockResolvedValue(undefined);

      await BackgroundJobSystem.startTournamentJob('tournament-1');
      await BackgroundJobSystem.startTournamentJob('tournament-2');

      const statuses = BackgroundJobSystem.getAllJobStatuses();
      expect(statuses).toHaveLength(2);
      expect(statuses.every(s => s.status === 'running')).toBe(true);
    });

    it('should return empty array when no jobs', () => {
      const statuses = BackgroundJobSystem.getAllJobStatuses();
      expect(statuses).toHaveLength(0);
    });
  });

  describe('getSystemStats', () => {
    it('should return correct system statistics', async () => {
      const mockTournament = {
        status: TournamentStatus.ACTIVE,
        name: 'Test Tournament',
      };

      (db.tournament.findUnique as Mock).mockResolvedValue(mockTournament);
      (MatchScheduler.startTournamentScheduler as Mock).mockResolvedValue(undefined);
      (MatchScheduler.getRunningMatches as Mock).mockReturnValue(['match-1', 'match-2']);

      // Start some jobs
      await BackgroundJobSystem.startTournamentJob('tournament-1');
      await BackgroundJobSystem.startTournamentJob('tournament-2');
      
      // Stop one job
      BackgroundJobSystem.stopTournamentJob('tournament-1');

      const stats = BackgroundJobSystem.getSystemStats();
      expect(stats).toEqual({
        totalJobs: 2,
        runningJobs: 1,
        stoppedJobs: 1,
        errorJobs: 0,
        runningMatches: 2,
      });
    });
  });

  describe('updateJobActivity', () => {
    it('should update last activity timestamp', async () => {
      const mockTournament = {
        status: TournamentStatus.ACTIVE,
        name: 'Test Tournament',
      };

      (db.tournament.findUnique as Mock).mockResolvedValue(mockTournament);
      (MatchScheduler.startTournamentScheduler as Mock).mockResolvedValue(undefined);

      await BackgroundJobSystem.startTournamentJob('test-tournament');
      
      const initialStatus = BackgroundJobSystem.getJobStatus('test-tournament');
      const initialActivity = initialStatus?.lastActivity;

      // Wait a bit and update activity
      await new Promise(resolve => setTimeout(resolve, 10));
      BackgroundJobSystem.updateJobActivity('test-tournament');

      const updatedStatus = BackgroundJobSystem.getJobStatus('test-tournament');
      expect(updatedStatus?.lastActivity).not.toEqual(initialActivity);
    });

    it('should not update activity for non-running jobs', async () => {
      const mockTournament = {
        status: TournamentStatus.ACTIVE,
        name: 'Test Tournament',
      };

      (db.tournament.findUnique as Mock).mockResolvedValue(mockTournament);
      (MatchScheduler.startTournamentScheduler as Mock).mockResolvedValue(undefined);

      await BackgroundJobSystem.startTournamentJob('test-tournament');
      BackgroundJobSystem.stopTournamentJob('test-tournament');
      
      const stoppedStatus = BackgroundJobSystem.getJobStatus('test-tournament');
      const lastActivity = stoppedStatus?.lastActivity;

      BackgroundJobSystem.updateJobActivity('test-tournament');

      const unchangedStatus = BackgroundJobSystem.getJobStatus('test-tournament');
      expect(unchangedStatus?.lastActivity).toEqual(lastActivity);
    });
  });

  describe('cleanupJobs', () => {
    it('should remove old completed jobs', async () => {
      // Create a mock old job
      const oldJob: JobStatus = {
        id: 'old-tournament',
        type: 'tournament-scheduler',
        status: 'stopped',
        startedAt: new Date(Date.now() - 25 * 60 * 60 * 1000), // 25 hours ago
        lastActivity: new Date(Date.now() - 25 * 60 * 60 * 1000),
      };

      (BackgroundJobSystem as any).jobs.set('old-tournament', oldJob);

      BackgroundJobSystem.cleanupJobs();

      const jobStatus = BackgroundJobSystem.getJobStatus('old-tournament');
      expect(jobStatus).toBeNull();
    });

    it('should keep recent completed jobs', async () => {
      const recentJob: JobStatus = {
        id: 'recent-tournament',
        type: 'tournament-scheduler',
        status: 'stopped',
        startedAt: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
        lastActivity: new Date(Date.now() - 1 * 60 * 60 * 1000),
      };

      (BackgroundJobSystem as any).jobs.set('recent-tournament', recentJob);

      BackgroundJobSystem.cleanupJobs();

      const jobStatus = BackgroundJobSystem.getJobStatus('recent-tournament');
      expect(jobStatus).toBeTruthy();
    });

    it('should never remove running jobs', async () => {
      const runningJob: JobStatus = {
        id: 'running-tournament',
        type: 'tournament-scheduler',
        status: 'running',
        startedAt: new Date(Date.now() - 25 * 60 * 60 * 1000), // 25 hours ago
        lastActivity: new Date(Date.now() - 25 * 60 * 60 * 1000),
      };

      (BackgroundJobSystem as any).jobs.set('running-tournament', runningJob);

      BackgroundJobSystem.cleanupJobs();

      const jobStatus = BackgroundJobSystem.getJobStatus('running-tournament');
      expect(jobStatus).toBeTruthy();
    });
  });

  describe('forceRestartJob', () => {
    it('should restart a job', async () => {
      const mockTournament = {
        status: TournamentStatus.ACTIVE,
        name: 'Test Tournament',
      };

      (db.tournament.findUnique as Mock).mockResolvedValue(mockTournament);
      (MatchScheduler.startTournamentScheduler as Mock).mockResolvedValue(undefined);

      // Start initial job
      await BackgroundJobSystem.startTournamentJob('test-tournament');
      
      // Force restart
      await BackgroundJobSystem.forceRestartJob('test-tournament');

      expect(MatchScheduler.stopTournamentScheduler).toHaveBeenCalledWith('test-tournament');
      expect(MatchScheduler.startTournamentScheduler).toHaveBeenCalledTimes(2);

      const jobStatus = BackgroundJobSystem.getJobStatus('test-tournament');
      expect(jobStatus?.status).toBe('running');
    });
  });

  describe('shutdown', () => {
    it('should stop all running jobs and cleanup', async () => {
      const mockTournament = {
        status: TournamentStatus.ACTIVE,
        name: 'Test Tournament',
      };

      (db.tournament.findUnique as Mock).mockResolvedValue(mockTournament);
      (MatchScheduler.startTournamentScheduler as Mock).mockResolvedValue(undefined);

      // Start multiple jobs
      await BackgroundJobSystem.startTournamentJob('tournament-1');
      await BackgroundJobSystem.startTournamentJob('tournament-2');

      BackgroundJobSystem.shutdown();

      expect(MatchScheduler.stopTournamentScheduler).toHaveBeenCalledWith('tournament-1');
      expect(MatchScheduler.stopTournamentScheduler).toHaveBeenCalledWith('tournament-2');

      const stats = BackgroundJobSystem.getSystemStats();
      expect(stats.runningJobs).toBe(0);
    });
  });
});