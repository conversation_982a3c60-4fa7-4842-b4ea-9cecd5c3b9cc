import { ELOCalculator, GameResult } from '../elo-calculator';

describe('ELOCalculator', () => {
  let calculator: ELOCalculator;

  beforeEach(() => {
    calculator = new ELOCalculator();
  });

  describe('getKFactor', () => {
    it('should return 100 for volatile period (0-4 games)', () => {
      expect(calculator.getKFactor(0)).toBe(100);
      expect(calculator.getKFactor(4)).toBe(100);
    });

    it('should return 50 for stabilizing period (5-9 games)', () => {
      expect(calculator.getKFactor(5)).toBe(50);
      expect(calculator.getKFactor(9)).toBe(50);
    });

    it('should return 20 for stable period (10+ games)', () => {
      expect(calculator.getKFactor(10)).toBe(20);
      expect(calculator.getKFactor(100)).toBe(20);
    });
  });

  describe('calculateExpectedScore', () => {
    it('should return 0.5 for equal ratings', () => {
      const expected = calculator.calculateExpectedScore(1900, 1900);
      expect(expected).toBeCloseTo(0.5, 3);
    });

    it('should return higher probability for higher-rated player', () => {
      const expected = calculator.calculateExpectedScore(2000, 1800);
      expect(expected).toBeGreaterThan(0.5);
      expect(expected).toBeCloseTo(0.76, 2);
    });

    it('should return lower probability for lower-rated player', () => {
      const expected = calculator.calculateExpectedScore(1800, 2000);
      expect(expected).toBeLessThan(0.5);
      expect(expected).toBeCloseTo(0.24, 2);
    });

    it('should handle extreme rating differences', () => {
      const expected = calculator.calculateExpectedScore(1000, 2000);
      expect(expected).toBeCloseTo(0.0, 2);
    });
  });

  describe('calculateEloChange', () => {
    describe('equal ratings with stable players', () => {
      it('should give ±10 points for win/loss when both players are stable and equal', () => {
        const result = calculator.calculateEloChange(1900, 1900, 10, 10, 'white');
        
        expect(result.whiteEloChange).toBe(10);
        expect(result.blackEloChange).toBe(-10);
        expect(result.whiteNewElo).toBe(1910);
        expect(result.blackNewElo).toBe(1890);
      });

      it('should give ±0 points for draw when both players are stable and equal', () => {
        const result = calculator.calculateEloChange(1900, 1900, 10, 10, 'draw');
        
        expect(result.whiteEloChange).toBe(0);
        expect(result.blackEloChange).toBe(0);
        expect(result.whiteNewElo).toBe(1900);
        expect(result.blackNewElo).toBe(1900);
      });
    });

    describe('200 point rating difference (1900 vs 1700)', () => {
      it('should give appropriate points based on rating difference and K-factor', () => {
        const result = calculator.calculateEloChange(1700, 1900, 10, 10, 'white');
        
        // Lower-rated player (white) wins - with K=20 for stable players
        // Expected score for 1700 vs 1900: ~0.24, so (1 - 0.24) * 20 = ~15
        expect(result.whiteEloChange).toBeCloseTo(15, 0);
        expect(result.blackEloChange).toBeCloseTo(-15, 0);
      });

      it('should give fewer points to higher-rated player for expected win', () => {
        const result = calculator.calculateEloChange(1900, 1700, 10, 10, 'white');
        
        // Higher-rated player (white) wins - with K=20 for stable players
        // Expected score for 1900 vs 1700: ~0.76, so (1 - 0.76) * 20 = ~5
        expect(result.whiteEloChange).toBeCloseTo(5, 0);
        expect(result.blackEloChange).toBeCloseTo(-5, 0);
      });

      it('should handle draw results correctly', () => {
        const result = calculator.calculateEloChange(1900, 1700, 10, 10, 'draw');
        
        // Higher-rated player loses points, lower-rated gains points
        expect(result.whiteEloChange).toBeLessThan(0);
        expect(result.blackEloChange).toBeGreaterThan(0);
        expect(Math.abs(result.whiteEloChange)).toBeCloseTo(Math.abs(result.blackEloChange), 0);
      });
    });

    describe('volatile period players', () => {
      it('should use K-factor of 100 for players with fewer than 5 games', () => {
        const result = calculator.calculateEloChange(1900, 1900, 0, 0, 'white');
        
        // With K=100, equal ratings, white win should be 50 points
        expect(result.whiteEloChange).toBe(50);
        expect(result.blackEloChange).toBe(-50);
      });

      it('should handle mixed experience levels', () => {
        // Volatile player (0 games) vs stable player (10 games)
        const result = calculator.calculateEloChange(1900, 1900, 0, 10, 'white');
        
        // Volatile player (white) gets bigger change due to K=100
        expect(Math.abs(result.whiteEloChange)).toBeGreaterThan(Math.abs(result.blackEloChange));
      });
    });

    describe('stabilizing period players', () => {
      it('should use K-factor of 50 for players with 5-9 games', () => {
        const result = calculator.calculateEloChange(1900, 1900, 7, 7, 'white');
        
        // With K=50, equal ratings, white win should be 25 points
        expect(result.whiteEloChange).toBe(25);
        expect(result.blackEloChange).toBe(-25);
      });
    });

    describe('edge cases and validation', () => {
      it('should enforce minimum ELO rating', () => {
        const result = calculator.calculateEloChange(150, 2000, 10, 10, 'black');
        
        // White player should not go below minimum ELO (100)
        expect(result.whiteNewElo).toBeGreaterThanOrEqual(100);
      });

      it('should cap maximum single-game change', () => {
        // Create extreme scenario that would normally exceed 200 points
        const result = calculator.calculateEloChange(100, 2800, 0, 0, 'white');
        
        // Changes should be capped at ±200
        expect(Math.abs(result.whiteEloChange)).toBeLessThanOrEqual(200);
        expect(Math.abs(result.blackEloChange)).toBeLessThanOrEqual(200);
      });

      it('should throw error for invalid game result', () => {
        expect(() => {
          calculator.calculateEloChange(1900, 1900, 10, 10, 'invalid' as GameResult);
        }).toThrow('Invalid game result: invalid');
      });

      it('should handle zero games played', () => {
        const result = calculator.calculateEloChange(1900, 1900, 0, 0, 'draw');
        
        expect(result.whiteEloChange).toBe(0);
        expect(result.blackEloChange).toBe(0);
      });
    });

    describe('mathematical consistency', () => {
      it('should ensure ELO changes sum to zero for equal K-factors', () => {
        const result = calculator.calculateEloChange(1900, 1900, 10, 10, 'white');
        
        // For equal K-factors, changes should sum to zero (conservation)
        expect(result.whiteEloChange + result.blackEloChange).toBe(0);
      });

      it('should be symmetric for player positions', () => {
        const result1 = calculator.calculateEloChange(1800, 2000, 10, 10, 'white');
        const result2 = calculator.calculateEloChange(2000, 1800, 10, 10, 'black');
        
        // Results should be equivalent when players and result are swapped
        expect(result1.whiteEloChange).toBe(result2.blackEloChange);
        expect(result1.blackEloChange).toBe(result2.whiteEloChange);
      });
    });
  });

  describe('configuration', () => {
    it('should use custom configuration', () => {
      const customCalculator = new ELOCalculator({
        volatileKFactor: 80,
        stabilizingKFactor: 40,
        stableKFactor: 15,
      });

      expect(customCalculator.getKFactor(0)).toBe(80);
      expect(customCalculator.getKFactor(7)).toBe(40);
      expect(customCalculator.getKFactor(15)).toBe(15);
    });

    it('should allow configuration updates', () => {
      calculator.updateConfig({ volatileKFactor: 120 });
      expect(calculator.getKFactor(0)).toBe(120);
    });

    it('should return current configuration', () => {
      const config = calculator.getConfig();
      expect(config.volatileKFactor).toBe(100);
      expect(config.stabilizingKFactor).toBe(50);
      expect(config.stableKFactor).toBe(20);
    });
  });

  describe('real-world scenarios', () => {
    it('should handle typical tournament match', () => {
      // Two experienced players with slight rating difference
      const result = calculator.calculateEloChange(2100, 2050, 25, 30, 'white');
      
      expect(result.whiteEloChange).toBeGreaterThan(0);
      expect(result.blackEloChange).toBeLessThan(0);
      expect(Math.abs(result.whiteEloChange)).toBeLessThan(15);
    });

    it('should handle new player vs experienced player', () => {
      // New player (volatile) vs experienced player
      const result = calculator.calculateEloChange(1900, 2200, 2, 50, 'white');
      
      // New player should have larger potential changes
      expect(Math.abs(result.whiteEloChange)).toBeGreaterThan(Math.abs(result.blackEloChange));
    });

    it('should handle upset victory correctly', () => {
      // Lower-rated player beats much higher-rated player
      const result = calculator.calculateEloChange(1600, 2000, 15, 20, 'white');
      
      expect(result.whiteEloChange).toBeGreaterThan(15); // Significant gain
      expect(result.blackEloChange).toBeLessThan(-15); // Significant loss
    });
  });
});