import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock Prisma before importing the service
vi.mock('@prisma/client', () => {
  const mockFindMany = vi.fn();
  return {
    PrismaClient: vi.fn().mockImplementation(() => ({
      match: {
        findMany: mockFindMany,
      },
    })),
    mockFindMany, // Export for use in tests
  };
});

import { mockFindMany } from '@prisma/client';
import {
  getMatchHistory,
  getMatchStatistics,
  exportMatchDataAsJSON,
  exportMatchDataAsCSV,
  getMatchAnalytics,
  type MatchHistoryFilters
} from '../match-history-service';

describe('Match History Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getMatchHistory', () => {
    it('should fetch match history with default parameters', async () => {
      const mockMatches = [
        {
          id: 'match1',
          tournamentId: 'tournament1',
          whiteProfileId: 'profile1',
          blackProfileId: 'profile2',
          round: 'Round 1',
          whiteEloChange: 25,
          blackEloChange: -25,
          scheduledAt: new Date('2024-01-01T10:00:00Z'),
          completedAt: new Date('2024-01-01T10:30:00Z'),
          createdAt: new Date('2024-01-01T09:00:00Z'),
          tournament: {
            id: 'tournament1',
            name: 'Test Tournament',
            format: 'ROUND_ROBIN'
          },
          whiteProfile: {
            id: 'profile1',
            name: 'GPT-4',
            model: 'gpt-4',
            eloRating: 1925
          },
          blackProfile: {
            id: 'profile2',
            name: 'Claude',
            model: 'claude-3',
            eloRating: 1875
          },
          game: {
            id: 'game1',
            result: '1-0',
            pgn: '1. e4 e5 2. Nf3 Nc6 3. Bb5 1-0',
            moveHistory: [{ move: 'e4' }],
            gameLogs: { log: 'Game completed' }
          }
        }
      ];

      mockFindMany.mockResolvedValue(mockMatches);

      const result = await getMatchHistory();

      expect(mockFindMany).toHaveBeenCalledWith({
        where: {
          status: 'COMPLETED',
          completedAt: { not: null }
        },
        include: {
          tournament: {
            select: {
              id: true,
              name: true,
              format: true
            }
          },
          whiteProfile: {
            select: {
              id: true,
              name: true,
              model: true,
              eloRating: true
            }
          },
          blackProfile: {
            select: {
              id: true,
              name: true,
              model: true,
              eloRating: true
            }
          },
          game: {
            select: {
              id: true,
              result: true,
              pgn: true,
              moveHistory: true,
              gameLogs: true
            }
          }
        },
        orderBy: { completedAt: 'desc' },
        take: 50,
        skip: 0
      });

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        id: 'match1',
        tournament: {
          id: 'tournament1',
          name: 'Test Tournament',
          format: 'ROUND_ROBIN'
        },
        whiteProfile: {
          id: 'profile1',
          name: 'GPT-4',
          model: 'gpt-4',
          eloRating: 1925
        },
        blackProfile: {
          id: 'profile2',
          name: 'Claude',
          model: 'claude-3',
          eloRating: 1875
        }
      });
    });

    it('should apply profile filter correctly', async () => {
      mockFindMany.mockResolvedValue([]);

      const filters: MatchHistoryFilters = {
        profileId: 'profile1',
        limit: 10
      };

      await getMatchHistory(filters);

      expect(mockFindMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: [
              { whiteProfileId: 'profile1' },
              { blackProfileId: 'profile1' }
            ]
          }),
          take: 10
        })
      );
    });

    it('should apply date range filter correctly', async () => {
      mockFindMany.mockResolvedValue([]);

      const dateFrom = new Date('2024-01-01');
      const dateTo = new Date('2024-01-31');
      const filters: MatchHistoryFilters = {
        dateFrom,
        dateTo
      };

      await getMatchHistory(filters);

      expect(mockFindMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            completedAt: {
              not: null,
              gte: dateFrom,
              lte: dateTo
            }
          })
        })
      );
    });

    it('should apply tournament filter correctly', async () => {
      mockFindMany.mockResolvedValue([]);

      const filters: MatchHistoryFilters = {
        tournamentId: 'tournament1'
      };

      await getMatchHistory(filters);

      expect(mockFindMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            tournamentId: 'tournament1'
          })
        })
      );
    });
  });

  describe('getMatchStatistics', () => {
    it('should calculate statistics correctly', async () => {
      const mockMatches = [
        {
          id: 'match1',
          whiteProfileId: 'profile1',
          blackProfileId: 'profile2',
          whiteEloChange: 25,
          blackEloChange: -25,
          completedAt: new Date('2024-01-01T10:30:00Z'),
          whiteProfile: { id: 'profile1', name: 'GPT-4', model: 'gpt-4', eloRating: 1925 },
          blackProfile: { id: 'profile2', name: 'Claude', model: 'claude-3', eloRating: 1875 },
          game: { result: '1-0' }
        },
        {
          id: 'match2',
          whiteProfileId: 'profile2',
          blackProfileId: 'profile1',
          whiteEloChange: -20,
          blackEloChange: 20,
          completedAt: new Date('2024-01-02T10:30:00Z'),
          whiteProfile: { id: 'profile2', name: 'Claude', model: 'claude-3', eloRating: 1855 },
          blackProfile: { id: 'profile1', name: 'GPT-4', model: 'gpt-4', eloRating: 1945 },
          game: { result: '0-1' }
        },
        {
          id: 'match3',
          whiteProfileId: 'profile1',
          blackProfileId: 'profile3',
          whiteEloChange: 0,
          blackEloChange: 0,
          completedAt: new Date('2024-01-03T10:30:00Z'),
          whiteProfile: { id: 'profile1', name: 'GPT-4', model: 'gpt-4', eloRating: 1945 },
          blackProfile: { id: 'profile3', name: 'Gemini', model: 'gemini-pro', eloRating: 1900 },
          game: { result: '1/2-1/2' }
        }
      ];

      mockFindMany.mockResolvedValue(mockMatches);

      const result = await getMatchStatistics('profile1');

      expect(result.totalMatches).toBe(3);
      expect(result.wins).toBe(2);
      expect(result.losses).toBe(0);
      expect(result.draws).toBe(1);
      expect(result.winRate).toBeCloseTo(0.667, 3);
      expect(result.averageEloChange).toBeCloseTo(15, 1);
      expect(result.currentStreak.type).toBe('draw');
      expect(result.currentStreak.count).toBe(1);
      expect(result.performanceTrends).toHaveLength(3);
      expect(result.headToHeadRecords).toHaveLength(2);
    });

    it('should handle empty match history', async () => {
      mockFindMany.mockResolvedValue([]);

      const result = await getMatchStatistics('profile1');

      expect(result.totalMatches).toBe(0);
      expect(result.wins).toBe(0);
      expect(result.losses).toBe(0);
      expect(result.draws).toBe(0);
      expect(result.winRate).toBe(0);
      expect(result.averageEloChange).toBe(0);
      expect(result.performanceTrends).toHaveLength(0);
      expect(result.headToHeadRecords).toHaveLength(0);
    });
  });

  describe('exportMatchDataAsJSON', () => {
    it('should export match data with statistics', async () => {
      const mockMatches = [
        {
          id: 'match1',
          tournament: { id: 'tournament1', name: 'Test Tournament', format: 'ROUND_ROBIN' },
          whiteProfile: { id: 'profile1', name: 'GPT-4', model: 'gpt-4', eloRating: 1925 },
          blackProfile: { id: 'profile2', name: 'Claude', model: 'claude-3', eloRating: 1875 },
          game: { id: 'game1', result: '1-0', pgn: '1. e4 1-0', moveHistory: null, gameLogs: null },
          round: 'Round 1',
          whiteEloChange: 25,
          blackEloChange: -25,
          scheduledAt: new Date(),
          completedAt: new Date(),
          createdAt: new Date()
        }
      ];

      mockFindMany.mockResolvedValue(mockMatches);

      const filters: MatchHistoryFilters = { profileId: 'profile1' };
      const result = await exportMatchDataAsJSON(filters);

      expect(result.matches).toHaveLength(1);
      expect(result.statistics).toBeDefined();
      expect(result.exportedAt).toBeInstanceOf(Date);
      expect(result.filters).toEqual(filters);
    });
  });

  describe('exportMatchDataAsCSV', () => {
    it('should export match data as CSV format', async () => {
      const mockMatches = [
        {
          id: 'match1',
          tournament: { id: 'tournament1', name: 'Test Tournament', format: 'ROUND_ROBIN' },
          whiteProfile: { id: 'profile1', name: 'GPT-4', model: 'gpt-4', eloRating: 1925 },
          blackProfile: { id: 'profile2', name: 'Claude', model: 'claude-3', eloRating: 1875 },
          game: { id: 'game1', result: '1-0', pgn: '1. e4 e5 2. Nf3 1-0', moveHistory: null, gameLogs: null },
          round: 'Round 1',
          whiteEloChange: 25,
          blackEloChange: -25,
          scheduledAt: new Date('2024-01-01T10:00:00Z'),
          completedAt: new Date('2024-01-01T10:30:00Z'),
          createdAt: new Date('2024-01-01T09:00:00Z')
        }
      ];

      mockFindMany.mockResolvedValue(mockMatches);

      const result = await exportMatchDataAsCSV();

      expect(result).toContain('Match ID,Tournament,Round,White Player');
      expect(result).toContain('match1,Test Tournament,Round 1,GPT-4');
      expect(result).toContain('Claude,claude-3,1-0,25,-25');
      expect(result).toContain('"1. e4 e5 2. Nf3 1-0"');
    });

    it('should handle CSV escaping correctly', async () => {
      const mockMatches = [
        {
          id: 'match1',
          tournament: null,
          whiteProfile: { id: 'profile1', name: 'GPT-4', model: 'gpt-4', eloRating: 1925 },
          blackProfile: { id: 'profile2', name: 'Claude', model: 'claude-3', eloRating: 1875 },
          game: { id: 'game1', result: '1-0', pgn: '1. e4 "best move" 1-0', moveHistory: null, gameLogs: null },
          round: null,
          whiteEloChange: null,
          blackEloChange: null,
          scheduledAt: null,
          completedAt: new Date('2024-01-01T10:30:00Z'),
          createdAt: new Date('2024-01-01T09:00:00Z')
        }
      ];

      mockFindMany.mockResolvedValue(mockMatches);

      const result = await exportMatchDataAsCSV();

      expect(result).toContain('N/A,N/A,GPT-4');
      expect(result).toContain('N/A,N/A,N/A');
      expect(result).toContain('"1. e4 ""best move"" 1-0"');
    });
  });

  describe('getMatchAnalytics', () => {
    it('should calculate analytics insights correctly', async () => {
      const mockMatches = Array.from({ length: 15 }, (_, i) => ({
        id: `match${i + 1}`,
        whiteProfileId: i % 2 === 0 ? 'profile1' : 'profile2',
        blackProfileId: i % 2 === 0 ? 'profile2' : 'profile1',
        whiteEloChange: i % 2 === 0 ? 20 : -20,
        blackEloChange: i % 2 === 0 ? -20 : 20,
        completedAt: new Date(Date.now() - (15 - i) * 24 * 60 * 60 * 1000), // Last 15 days
        whiteProfile: { 
          id: i % 2 === 0 ? 'profile1' : 'profile2', 
          name: i % 2 === 0 ? 'GPT-4' : 'Claude', 
          model: i % 2 === 0 ? 'gpt-4' : 'claude-3', 
          eloRating: 1900 + i * 10 
        },
        blackProfile: { 
          id: i % 2 === 0 ? 'profile2' : 'profile1', 
          name: i % 2 === 0 ? 'Claude' : 'GPT-4', 
          model: i % 2 === 0 ? 'claude-3' : 'gpt-4', 
          eloRating: 1900 + i * 10 
        },
        game: { result: i % 3 === 0 ? '1/2-1/2' : (i % 2 === 0 ? '1-0' : '0-1') }
      }));

      mockFindMany.mockResolvedValue(mockMatches);

      const result = await getMatchAnalytics('profile1');

      expect(result.recentPerformance.last10Games).toBeDefined();
      expect(result.recentPerformance.last30Days).toBeDefined();
      expect(result.strongestOpponents).toBeDefined();
      expect(result.weakestOpponents).toBeDefined();
      expect(result.eloProgression).toHaveLength(15);
      expect(result.eloProgression[0].date).toBeInstanceOf(Date);
      expect(typeof result.eloProgression[0].elo).toBe('number');
      expect(typeof result.eloProgression[0].change).toBe('number');
    });
  });
});