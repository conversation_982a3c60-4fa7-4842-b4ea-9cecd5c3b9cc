import { describe, it, expect, beforeEach, vi } from 'vitest';
import { 
  withRetry, 
  retryAIMove, 
  retryDatabaseOperation, 
  CircuitBreaker, 
  isRetryableError,
  createJitteredDelay,
  DEFAULT_RETRY_OPTIONS 
} from '../retry-utils';

describe('Retry Utils', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('withRetry', () => {
    it('should succeed on first attempt', async () => {
      const operation = vi.fn().mockResolvedValue('success');
      
      const result = await withRetry(operation);
      
      expect(result.success).toBe(true);
      expect(result.result).toBe('success');
      expect(result.attempts).toBe(1);
      expect(operation).toHaveBeenCalledTimes(1);
    });

    it('should retry on failure and eventually succeed', async () => {
      const operation = vi.fn()
        .mockRejectedValueOnce(new Error('temporary failure'))
        .mockResolvedValue('success');
      
      const result = await withRetry(operation, {
        maxAttempts: 3,
        baseDelay: 10,
        maxDelay: 100,
        backoffMultiplier: 2,
      });
      
      expect(result.success).toBe(true);
      expect(result.result).toBe('success');
      expect(result.attempts).toBe(2);
      expect(operation).toHaveBeenCalledTimes(2);
    });

    it('should fail after max attempts', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('persistent failure'));
      
      const result = await withRetry(operation, {
        maxAttempts: 2,
        baseDelay: 10,
        maxDelay: 100,
        backoffMultiplier: 2,
      });
      
      expect(result.success).toBe(false);
      expect(result.error?.message).toBe('persistent failure');
      expect(result.attempts).toBe(2);
      expect(operation).toHaveBeenCalledTimes(2);
    });

    it('should respect retry condition', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('Invalid move'));
      
      const result = await withRetry(operation, {
        maxAttempts: 3,
        baseDelay: 10,
        maxDelay: 100,
        backoffMultiplier: 2,
        retryCondition: (error) => !error.message.includes('Invalid move'),
      });
      
      expect(result.success).toBe(false);
      expect(result.attempts).toBe(1); // Should not retry
      expect(operation).toHaveBeenCalledTimes(1);
    });

    it('should apply exponential backoff', async () => {
      const operation = vi.fn()
        .mockRejectedValueOnce(new Error('failure 1'))
        .mockRejectedValueOnce(new Error('failure 2'))
        .mockResolvedValue('success');
      
      const startTime = Date.now();
      
      const result = await withRetry(operation, {
        maxAttempts: 3,
        baseDelay: 50,
        maxDelay: 200,
        backoffMultiplier: 2,
      });
      
      const endTime = Date.now();
      const totalTime = endTime - startTime;
      
      expect(result.success).toBe(true);
      expect(totalTime).toBeGreaterThan(100); // Should have waited at least 50ms + 100ms
    });
  });

  describe('retryAIMove', () => {
    it('should succeed with AI move generation', async () => {
      const operation = vi.fn().mockResolvedValue({ move: 'e4', reason: 'Good move' });
      
      const result = await retryAIMove(operation, { 
        player: 'white', 
        model: 'gemini-2.0-flash' 
      });
      
      expect(result).toEqual({ move: 'e4', reason: 'Good move' });
    });

    it('should not retry on invalid move errors', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('Invalid move: e9'));
      
      await expect(retryAIMove(operation, { 
        player: 'white', 
        model: 'gemini-2.0-flash' 
      })).rejects.toThrow('AI move generation failed after 1 attempts');
      
      expect(operation).toHaveBeenCalledTimes(1);
    });

    it('should retry on network errors', async () => {
      const operation = vi.fn()
        .mockRejectedValueOnce(new Error('Network timeout'))
        .mockResolvedValue({ move: 'e4', reason: 'Good move' });
      
      const result = await retryAIMove(operation, { 
        player: 'white', 
        model: 'gemini-2.0-flash' 
      });
      
      expect(result).toEqual({ move: 'e4', reason: 'Good move' });
      expect(operation).toHaveBeenCalledTimes(2);
    });
  });

  describe('retryDatabaseOperation', () => {
    it('should succeed with database operation', async () => {
      const operation = vi.fn().mockResolvedValue({ id: 'test-id' });
      
      const result = await retryDatabaseOperation(operation, { 
        operation: 'create_match' 
      });
      
      expect(result).toEqual({ id: 'test-id' });
    });

    it('should not retry on constraint violations', async () => {
      const error = new Error('Unique constraint violation');
      (error as any).code = 'P2002';
      const operation = vi.fn().mockRejectedValue(error);
      
      await expect(retryDatabaseOperation(operation, { 
        operation: 'create_match' 
      })).rejects.toThrow('Database operation failed after 1 attempts');
      
      expect(operation).toHaveBeenCalledTimes(1);
    });

    it('should retry on connection errors', async () => {
      const operation = vi.fn()
        .mockRejectedValueOnce(new Error('Connection lost'))
        .mockResolvedValue({ id: 'test-id' });
      
      const result = await retryDatabaseOperation(operation, { 
        operation: 'create_match' 
      });
      
      expect(result).toEqual({ id: 'test-id' });
      expect(operation).toHaveBeenCalledTimes(2);
    });
  });

  describe('CircuitBreaker', () => {
    it('should allow operations when closed', async () => {
      const circuitBreaker = new CircuitBreaker(3, 1000, 1);
      const operation = vi.fn().mockResolvedValue('success');
      
      const result = await circuitBreaker.execute(operation);
      
      expect(result).toBe('success');
      expect(circuitBreaker.getState().state).toBe('closed');
    });

    it('should open after failure threshold', async () => {
      const circuitBreaker = new CircuitBreaker(2, 1000, 1);
      const operation = vi.fn().mockRejectedValue(new Error('failure'));
      
      // First failure
      await expect(circuitBreaker.execute(operation)).rejects.toThrow('failure');
      expect(circuitBreaker.getState().state).toBe('closed');
      
      // Second failure - should open circuit
      await expect(circuitBreaker.execute(operation)).rejects.toThrow('failure');
      expect(circuitBreaker.getState().state).toBe('open');
      
      // Third attempt should be blocked
      await expect(circuitBreaker.execute(operation)).rejects.toThrow('Circuit breaker is open');
    });

    it('should transition to half-open after recovery timeout', async () => {
      const circuitBreaker = new CircuitBreaker(1, 100, 1); // 100ms recovery timeout
      const operation = vi.fn().mockRejectedValue(new Error('failure'));
      
      // Trigger circuit open
      await expect(circuitBreaker.execute(operation)).rejects.toThrow('failure');
      expect(circuitBreaker.getState().state).toBe('open');
      
      // Wait for recovery timeout
      await new Promise(resolve => setTimeout(resolve, 150));
      
      // Next operation should transition to half-open
      const successOperation = vi.fn().mockResolvedValue('success');
      const result = await circuitBreaker.execute(successOperation);
      
      expect(result).toBe('success');
      expect(circuitBreaker.getState().state).toBe('closed');
    });

    it('should reset manually', async () => {
      const circuitBreaker = new CircuitBreaker(1, 1000, 1);
      const operation = vi.fn().mockRejectedValue(new Error('failure'));
      
      // Open circuit
      await expect(circuitBreaker.execute(operation)).rejects.toThrow('failure');
      expect(circuitBreaker.getState().state).toBe('open');
      
      // Reset manually
      circuitBreaker.reset();
      expect(circuitBreaker.getState().state).toBe('closed');
      expect(circuitBreaker.getState().failures).toBe(0);
    });
  });

  describe('isRetryableError', () => {
    it('should identify network errors as retryable', () => {
      const networkError = new Error('Connection failed');
      (networkError as any).code = 'ECONNRESET';
      
      expect(isRetryableError(networkError)).toBe(true);
    });

    it('should identify 5xx HTTP errors as retryable', () => {
      const serverError = new Error('Internal server error');
      (serverError as any).status = 500;
      
      expect(isRetryableError(serverError)).toBe(true);
    });

    it('should identify rate limiting as retryable', () => {
      const rateLimitError = new Error('Too many requests');
      (rateLimitError as any).status = 429;
      
      expect(isRetryableError(rateLimitError)).toBe(true);
    });

    it('should identify timeout messages as retryable', () => {
      const timeoutError = new Error('Request timeout occurred');
      
      expect(isRetryableError(timeoutError)).toBe(true);
    });

    it('should not identify client errors as retryable', () => {
      const clientError = new Error('Bad request');
      (clientError as any).status = 400;
      
      expect(isRetryableError(clientError)).toBe(false);
    });
  });

  describe('createJitteredDelay', () => {
    it('should create delay with jitter', () => {
      const baseDelay = 1000;
      const jitterFactor = 0.1;
      
      const delays = Array.from({ length: 10 }, () => 
        createJitteredDelay(baseDelay, jitterFactor)
      );
      
      // All delays should be within jitter range
      delays.forEach(delay => {
        expect(delay).toBeGreaterThanOrEqual(900); // 1000 - 10%
        expect(delay).toBeLessThanOrEqual(1100);   // 1000 + 10%
      });
      
      // Delays should vary (not all the same)
      const uniqueDelays = new Set(delays);
      expect(uniqueDelays.size).toBeGreaterThan(1);
    });

    it('should never return negative delay', () => {
      const delay = createJitteredDelay(10, 2.0); // Large jitter factor
      expect(delay).toBeGreaterThanOrEqual(0);
    });
  });

  describe('DEFAULT_RETRY_OPTIONS', () => {
    it('should have appropriate defaults for AI moves', () => {
      const options = DEFAULT_RETRY_OPTIONS.aiMove;
      
      expect(options.maxAttempts).toBe(3);
      expect(options.baseDelay).toBe(1000);
      expect(options.maxDelay).toBe(5000);
      expect(options.backoffMultiplier).toBe(2);
      expect(options.retryCondition).toBeDefined();
    });

    it('should have appropriate defaults for database operations', () => {
      const options = DEFAULT_RETRY_OPTIONS.databaseOperation;
      
      expect(options.maxAttempts).toBe(5);
      expect(options.baseDelay).toBe(500);
      expect(options.maxDelay).toBe(3000);
      expect(options.backoffMultiplier).toBe(1.5);
      expect(options.retryCondition).toBeDefined();
    });

    it('should have appropriate defaults for match execution', () => {
      const options = DEFAULT_RETRY_OPTIONS.matchExecution;
      
      expect(options.maxAttempts).toBe(2);
      expect(options.baseDelay).toBe(2000);
      expect(options.maxDelay).toBe(10000);
      expect(options.backoffMultiplier).toBe(3);
      expect(options.retryCondition).toBeDefined();
    });
  });
});