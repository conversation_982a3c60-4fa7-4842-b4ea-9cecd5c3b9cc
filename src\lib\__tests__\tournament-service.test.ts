import { describe, it, expect, beforeEach, vi } from 'vitest';
import { TournamentService, CreateTournamentInput } from '../tournament-service';
import { TournamentFormat, TournamentStatus, MatchStatus } from '@prisma/client';

// Mock the database
vi.mock('../db', () => ({
  db: {
    $transaction: vi.fn(),
    tournament: {
      create: vi.fn(),
      findUnique: vi.fn(),
      findMany: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
    },
    tournamentParticipant: {
      create: vi.fn(),
    },
    match: {
      create: vi.fn(),
      findMany: vi.fn(),
      update: vi.fn(),
    },
  },
}));

describe('TournamentService', () => {
  const mockParticipants = [
    {
      id: '1',
      tournamentId: 'tournament-1',
      profileId: 'profile-1',
      seed: 1,
      eliminated: false,
      createdAt: new Date(),
      profile: {
        id: 'profile-1',
        name: 'GPT-4',
        model: 'openai/gpt-4',
        eloRating: 1900,
      },
    },
    {
      id: '2',
      tournamentId: 'tournament-1',
      profileId: 'profile-2',
      seed: 2,
      eliminated: false,
      createdAt: new Date(),
      profile: {
        id: 'profile-2',
        name: 'Claude-3',
        model: 'anthropic/claude-3',
        eloRating: 1850,
      },
    },
    {
      id: '3',
      tournamentId: 'tournament-1',
      profileId: 'profile-3',
      seed: 3,
      eliminated: false,
      createdAt: new Date(),
      profile: {
        id: 'profile-3',
        name: 'Gemini-Pro',
        model: 'google/gemini-pro',
        eloRating: 1800,
      },
    },
    {
      id: '4',
      tournamentId: 'tournament-1',
      profileId: 'profile-4',
      seed: 4,
      eliminated: false,
      createdAt: new Date(),
      profile: {
        id: 'profile-4',
        name: 'LLaMA-2',
        model: 'meta/llama-2',
        eloRating: 1750,
      },
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('generateBracket', () => {
    it('should generate round-robin bracket correctly', () => {
      const matches = TournamentService.generateBracket(
        TournamentFormat.ROUND_ROBIN,
        mockParticipants
      );

      // With 4 participants, should have 6 matches (4 choose 2)
      expect(matches).toHaveLength(6);

      // Check that each participant plays every other participant exactly once
      const pairings = new Set();
      matches.forEach(match => {
        const pair = [match.whiteProfileId, match.blackProfileId].sort().join('-');
        expect(pairings.has(pair)).toBe(false);
        pairings.add(pair);
      });

      // Verify all matches are scheduled
      matches.forEach(match => {
        expect(match.status).toBe(MatchStatus.SCHEDULED);
        expect(match.round).toMatch(/^Round \d+$/);
      });
    });

    it('should generate single elimination bracket correctly', () => {
      const matches = TournamentService.generateBracket(
        TournamentFormat.SINGLE_ELIMINATION,
        mockParticipants
      );

      // Should have first round matches plus subsequent rounds
      const firstRoundMatches = matches.filter(m => m.round === 'Round 1');
      expect(firstRoundMatches).toHaveLength(2); // 4 participants = 2 first round matches

      // Check for semifinal and final
      const semifinals = matches.filter(m => m.round === 'Semifinal');
      const finals = matches.filter(m => m.round === 'Final');
      expect(semifinals).toHaveLength(1);
      expect(finals).toHaveLength(1);

      // First round should have actual participant IDs
      firstRoundMatches.forEach(match => {
        expect(match.whiteProfileId).not.toBe('TBD');
        expect(match.blackProfileId).not.toBe('TBD');
      });

      // Later rounds should have TBD placeholders
      semifinals.forEach(match => {
        expect(match.whiteProfileId).toBe('TBD');
        expect(match.blackProfileId).toBe('TBD');
      });
    });

    it('should generate double elimination bracket correctly', () => {
      const matches = TournamentService.generateBracket(
        TournamentFormat.DOUBLE_ELIMINATION,
        mockParticipants
      );

      // Should have winners bracket, losers bracket, and grand final
      const wbRound1 = matches.filter(m => m.round === 'WB Round 1');
      const wbFinal = matches.filter(m => m.round === 'WB Final');
      const lbMatches = matches.filter(m => m.round.startsWith('LB Round'));
      const grandFinal = matches.filter(m => m.round === 'Grand Final');

      expect(wbRound1).toHaveLength(2); // 4 participants = 2 first round matches
      expect(wbFinal).toHaveLength(1);
      expect(lbMatches.length).toBeGreaterThan(0);
      expect(grandFinal).toHaveLength(1);

      // First round should have actual participant IDs
      wbRound1.forEach(match => {
        expect(match.whiteProfileId).not.toBe('TBD');
        expect(match.blackProfileId).not.toBe('TBD');
      });
    });

    it('should handle minimum participants (2)', () => {
      const twoParticipants = mockParticipants.slice(0, 2);
      
      const roundRobinMatches = TournamentService.generateBracket(
        TournamentFormat.ROUND_ROBIN,
        twoParticipants
      );
      expect(roundRobinMatches).toHaveLength(1);

      const singleElimMatches = TournamentService.generateBracket(
        TournamentFormat.SINGLE_ELIMINATION,
        twoParticipants
      );
      expect(singleElimMatches.length).toBeGreaterThan(0);
    });

    it('should throw error for unsupported format', () => {
      expect(() => {
        TournamentService.generateBracket(
          'UNSUPPORTED_FORMAT' as TournamentFormat,
          mockParticipants
        );
      }).toThrow('Unsupported tournament format');
    });
  });

  describe('checkParticipantAvailability', () => {
    it('should return availability status for participants', async () => {
      const { db } = await import('../db');
      
      // Mock active matches
      (db.match.findMany as any).mockResolvedValue([
        {
          whiteProfileId: 'profile-1',
          blackProfileId: 'profile-2',
        },
      ]);

      const availability = await TournamentService.checkParticipantAvailability([
        'profile-1',
        'profile-2',
        'profile-3',
        'profile-4',
      ]);

      expect(availability['profile-1']).toBe(false); // busy
      expect(availability['profile-2']).toBe(false); // busy
      expect(availability['profile-3']).toBe(true);  // available
      expect(availability['profile-4']).toBe(true);  // available
    });

    it('should return all available when no active matches', async () => {
      const { db } = await import('../db');
      
      (db.match.findMany as any).mockResolvedValue([]);

      const availability = await TournamentService.checkParticipantAvailability([
        'profile-1',
        'profile-2',
      ]);

      expect(availability['profile-1']).toBe(true);
      expect(availability['profile-2']).toBe(true);
    });
  });

  describe('CRUD operations', () => {
    it('should validate minimum participants on creation', async () => {
      const input: CreateTournamentInput = {
        name: 'Test Tournament',
        format: TournamentFormat.ROUND_ROBIN,
        participantIds: ['profile-1'], // Only 1 participant
      };

      await expect(TournamentService.createTournament(input)).rejects.toThrow(
        'Tournament must have at least 2 participants'
      );
    });

    it('should create tournament with proper defaults', async () => {
      const { db } = await import('../db');
      
      const mockTournament = {
        id: 'tournament-1',
        name: 'Test Tournament',
        format: TournamentFormat.ROUND_ROBIN,
        status: TournamentStatus.DRAFT,
        scheduledStart: null,
        matchInterval: 30,
        timeWindow: '00:00-23:59',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      (db.$transaction as any).mockImplementation(async (callback: any) => {
        const mockTx = {
          tournament: {
            create: vi.fn().mockResolvedValue(mockTournament),
          },
          tournamentParticipant: {
            create: vi.fn().mockImplementation((data: any) => ({
              ...data.data,
              id: `participant-${data.data.profileId}`,
              profile: mockParticipants.find(p => p.profileId === data.data.profileId)?.profile,
            })),
          },
          match: {
            create: vi.fn().mockImplementation((data: any) => ({
              ...data.data,
              id: `match-${Math.random()}`,
              whiteProfile: { id: data.data.whiteProfileId, name: 'Test', model: 'test' },
              blackProfile: { id: data.data.blackProfileId, name: 'Test', model: 'test' },
            })),
          },
        };
        return callback(mockTx);
      });

      const input: CreateTournamentInput = {
        name: 'Test Tournament',
        format: TournamentFormat.ROUND_ROBIN,
        participantIds: ['profile-1', 'profile-2'],
      };

      const result = await TournamentService.createTournament(input);

      expect(result.name).toBe('Test Tournament');
      expect(result.format).toBe(TournamentFormat.ROUND_ROBIN);
      expect(result.matchInterval).toBe(30);
      expect(result.timeWindow).toBe('00:00-23:59');
    });
  });

  describe('bracket generation edge cases', () => {
    it('should handle odd number of participants in single elimination', () => {
      const oddParticipants = mockParticipants.slice(0, 3);
      
      const matches = TournamentService.generateBracket(
        TournamentFormat.SINGLE_ELIMINATION,
        oddParticipants
      );

      // Should still generate valid bracket structure
      expect(matches.length).toBeGreaterThan(0);
      
      const firstRoundMatches = matches.filter(m => m.round === 'Round 1');
      expect(firstRoundMatches).toHaveLength(1); // Only one match possible with 3 participants
    });

    it('should handle large number of participants', () => {
      const largeParticipantList = Array.from({ length: 16 }, (_, i) => ({
        id: `${i + 1}`,
        tournamentId: 'tournament-1',
        profileId: `profile-${i + 1}`,
        seed: i + 1,
        eliminated: false,
        createdAt: new Date(),
        profile: {
          id: `profile-${i + 1}`,
          name: `Player ${i + 1}`,
          model: `model-${i + 1}`,
          eloRating: 1800 + i * 10,
        },
      }));

      const roundRobinMatches = TournamentService.generateBracket(
        TournamentFormat.ROUND_ROBIN,
        largeParticipantList
      );

      // 16 participants should generate 120 matches (16 choose 2)
      expect(roundRobinMatches).toHaveLength(120);

      const singleElimMatches = TournamentService.generateBracket(
        TournamentFormat.SINGLE_ELIMINATION,
        largeParticipantList
      );

      // Should have proper bracket structure for 16 participants
      const firstRound = singleElimMatches.filter(m => m.round === 'Round 1');
      expect(firstRound).toHaveLength(8); // 16 participants = 8 first round matches
    });
  });
});