import { TournamentStatus } from '@prisma/client';
import db from './db';
import { MatchScheduler } from './match-scheduler';

export interface JobStatus {
  id: string;
  type: 'tournament-scheduler' | 'match-execution';
  status: 'running' | 'stopped' | 'error';
  startedAt: Date;
  lastActivity?: Date;
  error?: string;
}

/**
 * Background Job System for managing automated tournament execution
 */
export class BackgroundJobSystem {
  private static jobs = new Map<string, JobStatus>();
  private static healthCheckInterval: NodeJS.Timeout | null = null;

  /**
   * Initialize the background job system
   */
  static initialize(): void {
    // Start health check system
    this.startHealthCheck();
    
    // Resume active tournaments on startup
    this.resumeActiveTournaments();
  }

  /**
   * Start a tournament scheduler job
   */
  static async startTournamentJob(tournamentId: string): Promise<void> {
    try {
      // Check if tournament exists and is active
      const tournament = await db.tournament.findUnique({
        where: { id: tournamentId },
        select: { status: true, name: true },
      });

      if (!tournament) {
        throw new Error('Tournament not found');
      }

      if (tournament.status !== TournamentStatus.ACTIVE) {
        throw new Error('Tournament is not active');
      }

      // Stop existing job if running
      this.stopTournamentJob(tournamentId);

      // Create job status
      const jobStatus: JobStatus = {
        id: tournamentId,
        type: 'tournament-scheduler',
        status: 'running',
        startedAt: new Date(),
        lastActivity: new Date(),
      };

      this.jobs.set(tournamentId, jobStatus);

      // Start the match scheduler
      await MatchScheduler.startTournamentScheduler(tournamentId);

      console.log(`Started tournament scheduler for: ${tournament.name} (${tournamentId})`);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      this.jobs.set(tournamentId, {
        id: tournamentId,
        type: 'tournament-scheduler',
        status: 'error',
        startedAt: new Date(),
        error: errorMessage,
      });

      throw error;
    }
  }

  /**
   * Stop a tournament scheduler job
   */
  static stopTournamentJob(tournamentId: string): void {
    const job = this.jobs.get(tournamentId);
    if (job) {
      MatchScheduler.stopTournamentScheduler(tournamentId);
      
      this.jobs.set(tournamentId, {
        ...job,
        status: 'stopped',
        lastActivity: new Date(),
      });

      console.log(`Stopped tournament scheduler for: ${tournamentId}`);
    }
  }

  /**
   * Get status of all jobs
   */
  static getAllJobStatuses(): JobStatus[] {
    return Array.from(this.jobs.values());
  }

  /**
   * Get status of a specific job
   */
  static getJobStatus(jobId: string): JobStatus | null {
    return this.jobs.get(jobId) || null;
  }

  /**
   * Resume active tournaments on system startup
   */
  private static async resumeActiveTournaments(): Promise<void> {
    try {
      const activeTournaments = await db.tournament.findMany({
        where: { status: TournamentStatus.ACTIVE },
        select: { id: true, name: true },
      });

      console.log(`Found ${activeTournaments.length} active tournaments to resume`);

      for (const tournament of activeTournaments) {
        try {
          await this.startTournamentJob(tournament.id);
          console.log(`Resumed tournament: ${tournament.name}`);
        } catch (error) {
          console.error(`Failed to resume tournament ${tournament.name}:`, error);
        }
      }
    } catch (error) {
      console.error('Failed to resume active tournaments:', error);
    }
  }

  /**
   * Health check system to monitor job status
   */
  private static startHealthCheck(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthCheck();
    }, 60000); // Check every minute

    console.log('Background job health check system started');
  }

  /**
   * Perform health check on all running jobs
   */
  private static async performHealthCheck(): Promise<void> {
    const now = new Date();
    const staleThreshold = 10 * 60 * 1000; // 10 minutes

    for (const [jobId, job] of this.jobs.entries()) {
      if (job.status === 'running') {
        // Check if job is stale (no activity for too long)
        const lastActivity = job.lastActivity || job.startedAt;
        const timeSinceActivity = now.getTime() - lastActivity.getTime();

        if (timeSinceActivity > staleThreshold) {
          console.warn(`Job ${jobId} appears stale, checking tournament status`);
          
          try {
            // Check if tournament is still active
            const tournament = await db.tournament.findUnique({
              where: { id: jobId },
              select: { status: true },
            });

            if (!tournament || tournament.status !== TournamentStatus.ACTIVE) {
              console.log(`Stopping stale job for inactive tournament: ${jobId}`);
              this.stopTournamentJob(jobId);
            } else {
              // Update last activity
              this.jobs.set(jobId, {
                ...job,
                lastActivity: now,
              });
            }
          } catch (error) {
            console.error(`Health check failed for job ${jobId}:`, error);
            this.jobs.set(jobId, {
              ...job,
              status: 'error',
              error: error instanceof Error ? error.message : 'Health check failed',
              lastActivity: now,
            });
          }
        }
      }
    }
  }

  /**
   * Update job activity timestamp
   */
  static updateJobActivity(jobId: string): void {
    const job = this.jobs.get(jobId);
    if (job && job.status === 'running') {
      this.jobs.set(jobId, {
        ...job,
        lastActivity: new Date(),
      });
    }
  }

  /**
   * Clean up completed or old jobs
   */
  static cleanupJobs(): void {
    const now = new Date();
    const cleanupThreshold = 24 * 60 * 60 * 1000; // 24 hours

    for (const [jobId, job] of this.jobs.entries()) {
      if (job.status !== 'running') {
        const timeSinceLastActivity = now.getTime() - (job.lastActivity || job.startedAt).getTime();
        
        if (timeSinceLastActivity > cleanupThreshold) {
          this.jobs.delete(jobId);
          console.log(`Cleaned up old job: ${jobId}`);
        }
      }
    }
  }

  /**
   * Shutdown the background job system
   */
  static async shutdown(): Promise<void> {
    // Stop health check
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    // Stop all running jobs
    for (const [jobId, job] of this.jobs.entries()) {
      if (job.status === 'running') {
        this.stopTournamentJob(jobId);
      }
    }

    // Ensure DB connections are closed gracefully
    try {
      await db.$disconnect();
      console.log('Prisma disconnected during background job shutdown');
    } catch (e) {
      console.error('Error disconnecting Prisma during shutdown', e);
    }

    console.log('Background job system shutdown complete');
  }

  /**
   * Get system statistics
   */
  static getSystemStats(): {
    totalJobs: number;
    runningJobs: number;
    stoppedJobs: number;
    errorJobs: number;
    runningMatches: number;
  } {
    const jobs = Array.from(this.jobs.values());
    
    return {
      totalJobs: jobs.length,
      runningJobs: jobs.filter(j => j.status === 'running').length,
      stoppedJobs: jobs.filter(j => j.status === 'stopped').length,
      errorJobs: jobs.filter(j => j.status === 'error').length,
      runningMatches: MatchScheduler.getRunningMatches().length,
    };
  }

  /**
   * Force restart a job (emergency use)
   */
  static async forceRestartJob(jobId: string): Promise<void> {
    console.log(`Force restarting job: ${jobId}`);
    
    this.stopTournamentJob(jobId);
    
    // Wait a moment for cleanup
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await this.startTournamentJob(jobId);
  }
}

// Initialize the system when the module is loaded
if (typeof window === 'undefined') {
  // Only initialize on server side
  BackgroundJobSystem.initialize();
}

// Graceful shutdown handling
if (typeof process !== 'undefined') {
  process.on('SIGINT', async () => {
    console.log('Received SIGINT, shutting down background jobs...');
    await BackgroundJobSystem.shutdown();
    process.exit(0);
  });

  process.on('SIGTERM', async () => {
    console.log('Received SIGTERM, shutting down background jobs...');
    await BackgroundJobSystem.shutdown();
    process.exit(0);
  });
}