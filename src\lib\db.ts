import { PrismaClient } from '@prisma/client';

// Declare a global variable to hold the Prisma Client instance.
// This is to ensure that in a development environment, where the module might be reloaded,
// we don't create a new connection pool with each reload.
declare global {
  // eslint-disable-next-line no-var
  var prisma: PrismaClient | undefined;
}

// Create a new PrismaClient instance if one doesn't already exist in the global scope.
// In a production environment, this will only run once.
// In development, this will reuse the existing 'global.prisma' instance across hot reloads.
const prisma = globalThis.prisma || new PrismaClient();

// If we are not in a production environment, assign the prisma instance to the global scope.
// This makes it available for reuse on subsequent hot reloads.
if (process.env.NODE_ENV !== 'production') {
  // @ts-expect-error - augmenting globalThis for Prisma reuse in dev
  globalThis.prisma = prisma;
}

// Graceful shutdown: ensure Prisma disconnects so Postgres doesn't see abrupt resets
let prismaShuttingDown = false;
const disconnectPrisma = async (reason: string) => {
  if (prismaShuttingDown) return;
  prismaShuttingDown = true;
  try {
    console.log(`[Prisma] Disconnecting due to ${reason}...`);
    await prisma.$disconnect();
    console.log('[Prisma] Disconnected');
  } catch (e) {
    console.error('[Prisma] Error during disconnect:', e);
  }
};

if (typeof process !== 'undefined') {
  // Before Node exits naturally, try to disconnect cleanly
  process.on('beforeExit', async () => {
    await disconnectPrisma('beforeExit');
  });

  // Handle termination signals (Railway sends SIGTERM on stop/redeploy)
  process.on('SIGINT', async () => {
    await disconnectPrisma('SIGINT');
  });
  process.on('SIGTERM', async () => {
    await disconnectPrisma('SIGTERM');
  });
}

export default prisma;
