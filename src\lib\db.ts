import { PrismaClient } from '@prisma/client';

// Declare a global variable to hold the Prisma Client instance.
// This is to ensure that in a development environment, where the module might be reloaded,
// we don't create a new connection pool with each reload.
declare global {
  // eslint-disable-next-line no-var
  var prisma: PrismaClient | undefined;
}

// Railway-optimized Prisma configuration
const createPrismaClient = () => {
  return new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
    // Railway-specific optimizations
    __internal: {
      engine: {
        // Reduce connection pool size for Railway's resource constraints
        connection_limit: parseInt(process.env.DATABASE_CONNECTION_LIMIT || '5'),
        // Shorter timeouts for Railway's network environment
        pool_timeout: parseInt(process.env.DATABASE_POOL_TIMEOUT || '10'),
        // Enable connection pooling optimizations
        schema_cache_size: 1000,
      },
    },
  });
};

// Create a new PrismaClient instance if one doesn't already exist in the global scope.
// In a production environment, this will only run once.
// In development, this will reuse the existing 'global.prisma' instance across hot reloads.
const prisma = globalThis.prisma || createPrismaClient();

// If we are not in a production environment, assign the prisma instance to the global scope.
// This makes it available for reuse on subsequent hot reloads.
if (process.env.NODE_ENV !== 'production') {
  // @ts-expect-error - augmenting globalThis for Prisma reuse in dev
  globalThis.prisma = prisma;
}

// Health check and connection management
let lastHealthCheck = 0;
const HEALTH_CHECK_INTERVAL = 30000; // 30 seconds

export const checkDatabaseHealth = async (): Promise<boolean> => {
  try {
    const now = Date.now();
    if (now - lastHealthCheck < HEALTH_CHECK_INTERVAL) {
      return true; // Skip frequent health checks
    }

    await prisma.$queryRaw`SELECT 1`;
    lastHealthCheck = now;
    return true;
  } catch (error) {
    console.error('[Prisma] Health check failed:', error);
    return false;
  }
};

// Connection retry logic for Railway's network environment
export const withDatabaseRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries = 3,
  baseDelay = 1000
): Promise<T> => {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error: any) {
      lastError = error;

      // Don't retry on certain errors
      if (error.code === 'P2002' || error.code === 'P2025') {
        throw error;
      }

      if (attempt === maxRetries) {
        console.error(`[Prisma] Operation failed after ${maxRetries} attempts:`, error);
        throw error;
      }

      const delay = baseDelay * Math.pow(2, attempt - 1);
      console.warn(`[Prisma] Attempt ${attempt} failed, retrying in ${delay}ms:`, error.message);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
};

// Graceful shutdown: ensure Prisma disconnects so Postgres doesn't see abrupt resets
let prismaShuttingDown = false;
const disconnectPrisma = async (reason: string) => {
  if (prismaShuttingDown) return;
  prismaShuttingDown = true;
  try {
    console.log(`[Prisma] Disconnecting due to ${reason}...`);
    await prisma.$disconnect();
    console.log('[Prisma] Disconnected');
  } catch (e) {
    console.error('[Prisma] Error during disconnect:', e);
  }
};

if (typeof process !== 'undefined') {
  // Before Node exits naturally, try to disconnect cleanly
  process.on('beforeExit', async () => {
    await disconnectPrisma('beforeExit');
  });

  // Handle termination signals (Railway sends SIGTERM on stop/redeploy)
  process.on('SIGINT', async () => {
    await disconnectPrisma('SIGINT');
    process.exit(0);
  });
  process.on('SIGTERM', async () => {
    await disconnectPrisma('SIGTERM');
    process.exit(0);
  });

  // Handle uncaught exceptions and unhandled rejections
  process.on('uncaughtException', async (error) => {
    console.error('[Prisma] Uncaught exception:', error);
    await disconnectPrisma('uncaughtException');
    process.exit(1);
  });

  process.on('unhandledRejection', async (reason, promise) => {
    console.error('[Prisma] Unhandled rejection at:', promise, 'reason:', reason);
    await disconnectPrisma('unhandledRejection');
  });
}

// Railway-specific: Periodic connection health check
if (process.env.NODE_ENV === 'production' && process.env.RAILWAY_ENVIRONMENT) {
  setInterval(async () => {
    try {
      await checkDatabaseHealth();
    } catch (error) {
      console.error('[Prisma] Periodic health check failed:', error);
    }
  }, HEALTH_CHECK_INTERVAL);
}

export default prisma;
