/**
 * ELO Rating Calculator
 * 
 * Implements the ELO rating system with configurable K-factors based on player experience.
 * Uses the standard ELO formula: ΔElo = K × (Result - Expected)
 */

export interface ELOCalculationResult {
  whiteEloChange: number;
  blackEloChange: number;
  whiteNewElo: number;
  blackNewElo: number;
}

export interface ELOCalculatorConfig {
  volatileGames: number; // Games threshold for volatile period (default: 5)
  stabilizingGames: number; // Games threshold for stabilizing period (default: 10)
  volatileKFactor: number; // K-factor for volatile period (default: 100)
  stabilizingKFactor: number; // K-factor for stabilizing period (default: 50)
  stableKFactor: number; // K-factor for stable period (default: 20)
  minimumElo: number; // Minimum ELO rating (default: 100)
  maximumChange: number; // Maximum single-game ELO change (default: 200)
}

export type GameResult = 'white' | 'black' | 'draw';

export class ELOCalculator {
  private config: ELOCalculatorConfig;

  constructor(config?: Partial<ELOCalculatorConfig>) {
    this.config = {
      volatileGames: 5,
      stabilizingGames: 10,
      volatileKFactor: 100,
      stabilizingKFactor: 50,
      stableKFactor: 20,
      minimumElo: 100,
      maximumChange: 200,
      ...config,
    };
  }

  /**
   * Calculate ELO changes for both players based on game result
   */
  calculateEloChange(
    whiteElo: number,
    blackElo: number,
    whiteGames: number,
    blackGames: number,
    result: GameResult
  ): ELOCalculationResult {
    // Get K-factors for both players
    const whiteKFactor = this.getKFactor(whiteGames);
    const blackKFactor = this.getKFactor(blackGames);

    // Calculate expected scores
    const whiteExpected = this.calculateExpectedScore(whiteElo, blackElo);
    const blackExpected = this.calculateExpectedScore(blackElo, whiteElo);

    // Determine actual scores based on result
    let whiteScore: number;
    let blackScore: number;

    switch (result) {
      case 'white':
        whiteScore = 1;
        blackScore = 0;
        break;
      case 'black':
        whiteScore = 0;
        blackScore = 1;
        break;
      case 'draw':
        whiteScore = 0.5;
        blackScore = 0.5;
        break;
      default:
        throw new Error(`Invalid game result: ${result}`);
    }

    // Calculate ELO changes
    let whiteEloChange = Math.round(whiteKFactor * (whiteScore - whiteExpected));
    let blackEloChange = Math.round(blackKFactor * (blackScore - blackExpected));

    // Apply maximum change limits
    whiteEloChange = Math.max(-this.config.maximumChange, Math.min(this.config.maximumChange, whiteEloChange));
    blackEloChange = Math.max(-this.config.maximumChange, Math.min(this.config.maximumChange, blackEloChange));

    // Calculate new ELO ratings
    let whiteNewElo = whiteElo + whiteEloChange;
    let blackNewElo = blackElo + blackEloChange;

    // Apply minimum ELO floor
    whiteNewElo = Math.max(this.config.minimumElo, whiteNewElo);
    blackNewElo = Math.max(this.config.minimumElo, blackNewElo);

    // Adjust changes if minimum ELO floor was applied
    whiteEloChange = whiteNewElo - whiteElo;
    blackEloChange = blackNewElo - blackElo;

    return {
      whiteEloChange,
      blackEloChange,
      whiteNewElo,
      blackNewElo,
    };
  }

  /**
   * Get K-factor based on number of games played
   */
  getKFactor(gamesPlayed: number): number {
    if (gamesPlayed < this.config.volatileGames) {
      return this.config.volatileKFactor; // Volatile period: 0-4 games
    } else if (gamesPlayed < this.config.stabilizingGames) {
      return this.config.stabilizingKFactor; // Stabilizing period: 5-9 games
    } else {
      return this.config.stableKFactor; // Stable period: 10+ games
    }
  }

  /**
   * Calculate expected score using ELO formula
   * Expected = 1 / (1 + 10^((OpponentElo - PlayerElo) / 400))
   */
  calculateExpectedScore(playerElo: number, opponentElo: number): number {
    const eloDifference = opponentElo - playerElo;
    return 1 / (1 + Math.pow(10, eloDifference / 400));
  }

  /**
   * Get the current configuration
   */
  getConfig(): ELOCalculatorConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<ELOCalculatorConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

// Export a default instance with standard configuration
export const defaultELOCalculator = new ELOCalculator();