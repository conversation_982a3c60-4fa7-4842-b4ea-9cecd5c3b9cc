// Client-side game service for fetching live games and history

export interface GameProfile {
  id: string;
  name: string;
  model?: string;
  eloRating: number;
}

export interface GameTournament {
  id: string;
  name: string;
  format: string;
  status?: string;
}

export interface GameActivity {
  id: string;
  type: string;
  whitePlayer: string;
  blackPlayer: string;
  whiteElo?: number;
  blackElo?: number;
  whiteEloChange?: number;
  blackEloChange?: number;
  result?: string;
  move?: string;
  reasoning?: string;
  metadata?: any;
  createdAt: string;
}

export interface Game {
  id: string;
  white: string;
  black: string;
  result?: string;
  pgn: string;
  status: 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
  createdAt: string;
  updatedAt: string;
  moveHistory?: any;
  gameLogs?: any;
  // Computed fields
  isLive: boolean;
  duration: number; // in seconds
  moveCount: number;
  // Related data
  tournament?: GameTournament;
  whiteProfile?: GameProfile;
  blackProfile?: GameProfile;
  activities?: GameActivity[];
}

export interface GameListResponse {
  games: Game[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

export class GameClientService {
  async getAllGames(options: {
    status?: 'live' | 'completed' | 'all';
    limit?: number;
    offset?: number;
  } = {}): Promise<GameListResponse> {
    try {
      const params = new URLSearchParams();
      
      if (options.status) params.append('status', options.status);
      if (options.limit) params.append('limit', options.limit.toString());
      if (options.offset) params.append('offset', options.offset.toString());

      const response = await fetch(`/api/games?${params.toString()}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch games: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error fetching games:', error);
      throw error;
    }
  }

  async getLiveGames(limit = 20, offset = 0): Promise<GameListResponse> {
    return this.getAllGames({ status: 'live', limit, offset });
  }

  async getCompletedGames(limit = 20, offset = 0): Promise<GameListResponse> {
    return this.getAllGames({ status: 'completed', limit, offset });
  }

  async getGameById(gameId: string): Promise<Game> {
    try {
      const response = await fetch(`/api/games/${gameId}`);
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Game not found');
        }
        throw new Error(`Failed to fetch game: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error fetching game:', error);
      throw error;
    }
  }

  async createGame(data: {
    white: string;
    black: string;
    pgn?: string;
    status?: 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
  }): Promise<Game> {
    try {
      const response = await fetch('/api/games', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Failed to create game: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating game:', error);
      throw error;
    }
  }

  async updateGame(gameId: string, data: Partial<Game>): Promise<Game> {
    try {
      const response = await fetch(`/api/games/${gameId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Failed to update game: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error updating game:', error);
      throw error;
    }
  }

  async deleteGame(gameId: string): Promise<void> {
    try {
      const response = await fetch(`/api/games/${gameId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Failed to delete game: ${response.status}`);
      }
    } catch (error) {
      console.error('Error deleting game:', error);
      throw error;
    }
  }

  // Utility methods
  formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  }

  getGameStatusBadge(status: string): { variant: string; text: string } {
    switch (status) {
      case 'IN_PROGRESS':
        return { variant: 'default', text: 'Live' };
      case 'COMPLETED':
        return { variant: 'success', text: 'Completed' };
      case 'FAILED':
        return { variant: 'destructive', text: 'Failed' };
      default:
        return { variant: 'secondary', text: 'Unknown' };
    }
  }

  getResultDisplay(result?: string): string {
    switch (result) {
      case '1-0':
        return 'White wins';
      case '0-1':
        return 'Black wins';
      case '1/2-1/2':
        return 'Draw';
      case '*':
        return 'In progress';
      default:
        return 'Unknown';
    }
  }
}

// Export singleton instance
export const gameService = new GameClientService();
