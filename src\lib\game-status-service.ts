import prisma, { withDatabaseRetry } from '@/lib/db';
import { GameStatus } from '@prisma/client';

export interface GameStatusUpdate {
  gameId: string;
  status: GameStatus;
  pgn?: string;
  result?: string;
  reasoningHistory?: any[];
  moveCount?: number;
  lastMoveAt?: Date;
}

export interface LiveGameInfo {
  id: string;
  white: string;
  black: string;
  status: GameStatus;
  pgn: string;
  result?: string;
  createdAt: Date;
  lastMoveAt?: Date;
  moveCount: number;
  spectatorCount: number;
}

/**
 * Service for managing game status and live game tracking
 */
export class GameStatusService {
  private static spectatorCounts = new Map<string, number>();

  /**
   * Update game status with retry logic
   */
  static async updateGameStatus(update: GameStatusUpdate): Promise<void> {
    await withDatabaseRetry(async () => {
      await prisma.game.update({
        where: { id: update.gameId },
        data: {
          status: update.status,
          ...(update.pgn && { pgn: update.pgn }),
          ...(update.result && { result: update.result }),
          ...(update.reasoningHistory && { reasoningHistory: update.reasoningHistory }),
          ...(update.lastMoveAt && { lastMoveAt: update.lastMoveAt }),
        },
      });
    });
  }

  /**
   * Mark game as completed
   */
  static async completeGame(gameId: string, result: string, pgn: string): Promise<void> {
    await this.updateGameStatus({
      gameId,
      status: GameStatus.COMPLETED,
      result,
      pgn,
      lastMoveAt: new Date(),
    });
  }

  /**
   * Mark game as failed
   */
  static async failGame(gameId: string, reason?: string): Promise<void> {
    await this.updateGameStatus({
      gameId,
      status: GameStatus.FAILED,
      result: '*',
    });
  }

  /**
   * Get all live games with spectator counts
   */
  static async getLiveGames(): Promise<LiveGameInfo[]> {
    const games = await withDatabaseRetry(async () => {
      return await prisma.game.findMany({
        where: {
          status: GameStatus.IN_PROGRESS,
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 50, // Limit to prevent performance issues
      });
    });

    return games.map(game => ({
      id: game.id,
      white: game.white,
      black: game.black,
      status: game.status,
      pgn: game.pgn,
      result: game.result || undefined,
      createdAt: game.createdAt,
      lastMoveAt: game.lastMoveAt || undefined,
      moveCount: this.countMoves(game.pgn),
      spectatorCount: this.spectatorCounts.get(game.id) || 0,
    }));
  }

  /**
   * Get game by ID with live status
   */
  static async getGameWithStatus(gameId: string): Promise<LiveGameInfo | null> {
    const game = await withDatabaseRetry(async () => {
      return await prisma.game.findUnique({
        where: { id: gameId },
      });
    });

    if (!game) return null;

    return {
      id: game.id,
      white: game.white,
      black: game.black,
      status: game.status,
      pgn: game.pgn,
      result: game.result || undefined,
      createdAt: game.createdAt,
      lastMoveAt: game.lastMoveAt || undefined,
      moveCount: this.countMoves(game.pgn),
      spectatorCount: this.spectatorCounts.get(game.id) || 0,
    };
  }

  /**
   * Add spectator to game
   */
  static addSpectator(gameId: string): void {
    const current = this.spectatorCounts.get(gameId) || 0;
    this.spectatorCounts.set(gameId, current + 1);
  }

  /**
   * Remove spectator from game
   */
  static removeSpectator(gameId: string): void {
    const current = this.spectatorCounts.get(gameId) || 0;
    if (current > 0) {
      this.spectatorCounts.set(gameId, current - 1);
    }
    
    // Clean up if no spectators
    if (this.spectatorCounts.get(gameId) === 0) {
      this.spectatorCounts.delete(gameId);
    }
  }

  /**
   * Get spectator count for a game
   */
  static getSpectatorCount(gameId: string): number {
    return this.spectatorCounts.get(gameId) || 0;
  }

  /**
   * Clean up completed games from spectator tracking
   */
  static async cleanupCompletedGames(): Promise<void> {
    const completedGames = await withDatabaseRetry(async () => {
      return await prisma.game.findMany({
        where: {
          status: {
            in: [GameStatus.COMPLETED, GameStatus.FAILED],
          },
        },
        select: { id: true },
      });
    });

    completedGames.forEach(game => {
      this.spectatorCounts.delete(game.id);
    });
  }

  /**
   * Count moves in PGN
   */
  private static countMoves(pgn: string): number {
    if (!pgn) return 0;
    
    // Simple move counting - count move numbers
    const moveMatches = pgn.match(/\d+\./g);
    return moveMatches ? moveMatches.length : 0;
  }

  /**
   * Get game statistics
   */
  static async getGameStatistics(): Promise<{
    totalGames: number;
    liveGames: number;
    completedGames: number;
    failedGames: number;
    totalSpectators: number;
  }> {
    const stats = await withDatabaseRetry(async () => {
      return await prisma.game.groupBy({
        by: ['status'],
        _count: {
          id: true,
        },
      });
    });

    const result = {
      totalGames: 0,
      liveGames: 0,
      completedGames: 0,
      failedGames: 0,
      totalSpectators: Array.from(this.spectatorCounts.values()).reduce((sum, count) => sum + count, 0),
    };

    stats.forEach(stat => {
      result.totalGames += stat._count.id;
      
      switch (stat.status) {
        case GameStatus.IN_PROGRESS:
          result.liveGames = stat._count.id;
          break;
        case GameStatus.COMPLETED:
          result.completedGames = stat._count.id;
          break;
        case GameStatus.FAILED:
          result.failedGames = stat._count.id;
          break;
      }
    });

    return result;
  }
}

// Periodic cleanup of completed games (run every 5 minutes)
if (typeof process !== 'undefined' && process.env.NODE_ENV === 'production') {
  setInterval(() => {
    GameStatusService.cleanupCompletedGames().catch(error => {
      console.error('Error cleaning up completed games:', error);
    });
  }, 5 * 60 * 1000);
}
