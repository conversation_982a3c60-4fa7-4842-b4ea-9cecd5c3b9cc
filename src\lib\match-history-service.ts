import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export interface MatchHistoryFilters {
  profileId?: string;
  tournamentId?: string;
  dateFrom?: Date;
  dateTo?: Date;
  result?: 'win' | 'loss' | 'draw';
  opponent?: string;
  limit?: number;
  offset?: number;
}

export interface MatchHistoryEntry {
  id: string;
  tournament?: {
    id: string;
    name: string;
    format: string;
  };
  whiteProfile: {
    id: string;
    name: string;
    model: string;
    eloRating: number;
  };
  blackProfile: {
    id: string;
    name: string;
    model: string;
    eloRating: number;
  };
  game: {
    id: string;
    result: string | null;
    pgn: string;
    moveHistory: any;
    gameLogs: any;
  };
  round?: string;
  whiteEloChange: number | null;
  blackEloChange: number | null;
  scheduledAt: Date | null;
  completedAt: Date | null;
  createdAt: Date;
}

export interface PerformanceTrend {
  date: Date;
  eloRating: number;
  result: 'win' | 'loss' | 'draw';
  opponent: string;
  eloChange: number;
}

export interface HeadToHeadRecord {
  opponent: {
    id: string;
    name: string;
    model: string;
  };
  totalGames: number;
  wins: number;
  losses: number;
  draws: number;
  winRate: number;
  averageEloChange: number;
  lastPlayed: Date | null;
}

export interface MatchStatistics {
  totalMatches: number;
  wins: number;
  losses: number;
  draws: number;
  winRate: number;
  averageEloChange: number;
  currentStreak: {
    type: 'win' | 'loss' | 'draw';
    count: number;
  };
  performanceTrends: PerformanceTrend[];
  headToHeadRecords: HeadToHeadRecord[];
}

export interface ExportData {
  matches: MatchHistoryEntry[];
  statistics: MatchStatistics;
  exportedAt: Date;
  filters: MatchHistoryFilters;
}

/**
 * Fetch match history with filtering options
 */
export async function getMatchHistory(filters: MatchHistoryFilters = {}): Promise<MatchHistoryEntry[]> {
  const {
    profileId,
    tournamentId,
    dateFrom,
    dateTo,
    result,
    opponent,
    limit = 50,
    offset = 0
  } = filters;

  const whereClause: any = {
    status: 'COMPLETED',
    completedAt: {
      not: null
    }
  };

  // Filter by profile (either as white or black player)
  if (profileId) {
    whereClause.OR = [
      { whiteProfileId: profileId },
      { blackProfileId: profileId }
    ];
  }

  // Filter by tournament
  if (tournamentId) {
    whereClause.tournamentId = tournamentId;
  }

  // Filter by date range
  if (dateFrom || dateTo) {
    whereClause.completedAt = {
      ...whereClause.completedAt,
      ...(dateFrom && { gte: dateFrom }),
      ...(dateTo && { lte: dateTo })
    };
  }

  // Filter by opponent name
  if (opponent) {
    whereClause.OR = [
      {
        whiteProfile: {
          name: {
            contains: opponent,
            mode: 'insensitive'
          }
        }
      },
      {
        blackProfile: {
          name: {
            contains: opponent,
            mode: 'insensitive'
          }
        }
      }
    ];
  }

  const matches = await prisma.match.findMany({
    where: whereClause,
    include: {
      tournament: {
        select: {
          id: true,
          name: true,
          format: true
        }
      },
      whiteProfile: {
        select: {
          id: true,
          name: true,
          model: true,
          eloRating: true
        }
      },
      blackProfile: {
        select: {
          id: true,
          name: true,
          model: true,
          eloRating: true
        }
      },
      game: {
        select: {
          id: true,
          result: true,
          pgn: true,
          moveHistory: true,
          gameLogs: true
        }
      }
    },
    orderBy: {
      completedAt: 'desc'
    },
    take: limit,
    skip: offset
  });

  // Filter by result if specified
  let filteredMatches = matches;
  if (result && profileId) {
    filteredMatches = matches.filter(match => {
      const gameResult = match.game?.result;
      if (!gameResult) return false;

      const isWhite = match.whiteProfileId === profileId;
      
      if (result === 'win') {
        return (isWhite && gameResult === '1-0') || (!isWhite && gameResult === '0-1');
      } else if (result === 'loss') {
        return (isWhite && gameResult === '0-1') || (!isWhite && gameResult === '1-0');
      } else if (result === 'draw') {
        return gameResult === '1/2-1/2';
      }
      
      return false;
    });
  }

  return filteredMatches.map(match => ({
    id: match.id,
    tournament: match.tournament || undefined,
    whiteProfile: match.whiteProfile,
    blackProfile: match.blackProfile,
    game: match.game || {
      id: '',
      result: null,
      pgn: '',
      moveHistory: null,
      gameLogs: null
    },
    round: match.round || undefined,
    whiteEloChange: match.whiteEloChange,
    blackEloChange: match.blackEloChange,
    scheduledAt: match.scheduledAt,
    completedAt: match.completedAt,
    createdAt: match.createdAt
  }));
}

/**
 * Calculate match statistics for a specific profile
 */
export async function getMatchStatistics(profileId: string, dateFrom?: Date, dateTo?: Date): Promise<MatchStatistics> {
  const whereClause: any = {
    status: 'COMPLETED',
    completedAt: { not: null },
    OR: [
      { whiteProfileId: profileId },
      { blackProfileId: profileId }
    ]
  };

  if (dateFrom || dateTo) {
    whereClause.completedAt = {
      ...whereClause.completedAt,
      ...(dateFrom && { gte: dateFrom }),
      ...(dateTo && { lte: dateTo })
    };
  }

  const matches = await prisma.match.findMany({
    where: whereClause,
    include: {
      whiteProfile: true,
      blackProfile: true,
      game: true
    },
    orderBy: {
      completedAt: 'asc'
    }
  });

  let wins = 0;
  let losses = 0;
  let draws = 0;
  let totalEloChange = 0;
  const performanceTrends: PerformanceTrend[] = [];
  const opponentStats = new Map<string, {
    opponent: { id: string; name: string; model: string };
    wins: number;
    losses: number;
    draws: number;
    totalEloChange: number;
    lastPlayed: Date | null;
  }>();

  // Calculate statistics from matches
  for (const match of matches) {
    if (!match.game?.result || !match.completedAt) continue;

    const isWhite = match.whiteProfileId === profileId;
    const opponent = isWhite ? match.blackProfile : match.whiteProfile;
    const eloChange = isWhite ? match.whiteEloChange : match.blackEloChange;
    const gameResult = match.game.result;

    let result: 'win' | 'loss' | 'draw';
    if (gameResult === '1/2-1/2') {
      draws++;
      result = 'draw';
    } else if ((isWhite && gameResult === '1-0') || (!isWhite && gameResult === '0-1')) {
      wins++;
      result = 'win';
    } else {
      losses++;
      result = 'loss';
    }

    if (eloChange !== null) {
      totalEloChange += eloChange;
    }

    // Add to performance trends
    performanceTrends.push({
      date: match.completedAt,
      eloRating: isWhite ? match.whiteProfile.eloRating : match.blackProfile.eloRating,
      result,
      opponent: opponent.name,
      eloChange: eloChange || 0
    });

    // Track head-to-head records
    const opponentKey = opponent.id;
    if (!opponentStats.has(opponentKey)) {
      opponentStats.set(opponentKey, {
        opponent: {
          id: opponent.id,
          name: opponent.name,
          model: opponent.model
        },
        wins: 0,
        losses: 0,
        draws: 0,
        totalEloChange: 0,
        lastPlayed: null
      });
    }

    const opponentStat = opponentStats.get(opponentKey)!;
    if (result === 'win') opponentStat.wins++;
    else if (result === 'loss') opponentStat.losses++;
    else opponentStat.draws++;
    
    if (eloChange !== null) {
      opponentStat.totalEloChange += eloChange;
    }
    opponentStat.lastPlayed = match.completedAt;
  }

  // Calculate current streak
  let currentStreak = { type: 'draw' as 'win' | 'loss' | 'draw', count: 0 };
  if (performanceTrends.length > 0) {
    const reversedTrends = [...performanceTrends].reverse();
    const lastResult = reversedTrends[0].result;
    currentStreak.type = lastResult;
    currentStreak.count = 1;

    for (let i = 1; i < reversedTrends.length; i++) {
      if (reversedTrends[i].result === lastResult) {
        currentStreak.count++;
      } else {
        break;
      }
    }
  }

  // Convert opponent stats to head-to-head records
  const headToHeadRecords: HeadToHeadRecord[] = Array.from(opponentStats.values()).map(stat => {
    const totalGames = stat.wins + stat.losses + stat.draws;
    return {
      opponent: stat.opponent,
      totalGames,
      wins: stat.wins,
      losses: stat.losses,
      draws: stat.draws,
      winRate: totalGames > 0 ? stat.wins / totalGames : 0,
      averageEloChange: totalGames > 0 ? stat.totalEloChange / totalGames : 0,
      lastPlayed: stat.lastPlayed
    };
  }).sort((a, b) => b.totalGames - a.totalGames);

  const totalMatches = wins + losses + draws;
  
  return {
    totalMatches,
    wins,
    losses,
    draws,
    winRate: totalMatches > 0 ? wins / totalMatches : 0,
    averageEloChange: totalMatches > 0 ? totalEloChange / totalMatches : 0,
    currentStreak,
    performanceTrends,
    headToHeadRecords
  };
}

/**
 * Export match data in JSON format
 */
export async function exportMatchDataAsJSON(filters: MatchHistoryFilters = {}): Promise<ExportData> {
  const matches = await getMatchHistory({ ...filters, limit: 1000 }); // Export more data
  
  let statistics: MatchStatistics = {
    totalMatches: 0,
    wins: 0,
    losses: 0,
    draws: 0,
    winRate: 0,
    averageEloChange: 0,
    currentStreak: { type: 'draw', count: 0 },
    performanceTrends: [],
    headToHeadRecords: []
  };

  if (filters.profileId) {
    statistics = await getMatchStatistics(filters.profileId, filters.dateFrom, filters.dateTo);
  }

  return {
    matches,
    statistics,
    exportedAt: new Date(),
    filters
  };
}

/**
 * Export match data in CSV format
 */
export async function exportMatchDataAsCSV(filters: MatchHistoryFilters = {}): Promise<string> {
  const matches = await getMatchHistory({ ...filters, limit: 1000 });

  const headers = [
    'Match ID',
    'Tournament',
    'Round',
    'White Player',
    'White Model',
    'Black Player', 
    'Black Model',
    'Result',
    'White ELO Change',
    'Black ELO Change',
    'Completed At',
    'Game PGN'
  ];

  const rows = matches.map(match => [
    match.id,
    match.tournament?.name || 'N/A',
    match.round || 'N/A',
    match.whiteProfile.name,
    match.whiteProfile.model,
    match.blackProfile.name,
    match.blackProfile.model,
    match.game.result || 'N/A',
    match.whiteEloChange?.toString() || 'N/A',
    match.blackEloChange?.toString() || 'N/A',
    match.completedAt?.toISOString() || 'N/A',
    `"${match.game.pgn.replace(/"/g, '""')}"` // Escape quotes in PGN
  ]);

  const csvContent = [headers, ...rows]
    .map(row => row.join(','))
    .join('\n');

  return csvContent;
}

/**
 * Get analytics insights for a profile
 */
export async function getMatchAnalytics(profileId: string): Promise<{
  recentPerformance: {
    last10Games: { wins: number; losses: number; draws: number; winRate: number };
    last30Days: { wins: number; losses: number; draws: number; winRate: number };
  };
  strongestOpponents: Array<{
    opponent: string;
    model: string;
    winRate: number;
    totalGames: number;
  }>;
  weakestOpponents: Array<{
    opponent: string;
    model: string;
    winRate: number;
    totalGames: number;
  }>;
  eloProgression: Array<{
    date: Date;
    elo: number;
    change: number;
  }>;
}> {
  const statistics = await getMatchStatistics(profileId);
  
  // Recent performance (last 10 games)
  const last10Trends = statistics.performanceTrends.slice(-10);
  const last10Stats = {
    wins: last10Trends.filter(t => t.result === 'win').length,
    losses: last10Trends.filter(t => t.result === 'loss').length,
    draws: last10Trends.filter(t => t.result === 'draw').length,
    winRate: 0
  };
  last10Stats.winRate = last10Trends.length > 0 ? last10Stats.wins / last10Trends.length : 0;

  // Last 30 days performance
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  const last30DaysTrends = statistics.performanceTrends.filter(t => t.date >= thirtyDaysAgo);
  const last30DaysStats = {
    wins: last30DaysTrends.filter(t => t.result === 'win').length,
    losses: last30DaysTrends.filter(t => t.result === 'loss').length,
    draws: last30DaysTrends.filter(t => t.result === 'draw').length,
    winRate: 0
  };
  last30DaysStats.winRate = last30DaysTrends.length > 0 ? last30DaysStats.wins / last30DaysTrends.length : 0;

  // Strongest opponents (lowest win rate, min 3 games)
  const strongestOpponents = statistics.headToHeadRecords
    .filter(record => record.totalGames >= 3)
    .sort((a, b) => a.winRate - b.winRate)
    .slice(0, 5)
    .map(record => ({
      opponent: record.opponent.name,
      model: record.opponent.model,
      winRate: record.winRate,
      totalGames: record.totalGames
    }));

  // Weakest opponents (highest win rate, min 3 games)
  const weakestOpponents = statistics.headToHeadRecords
    .filter(record => record.totalGames >= 3)
    .sort((a, b) => b.winRate - a.winRate)
    .slice(0, 5)
    .map(record => ({
      opponent: record.opponent.name,
      model: record.opponent.model,
      winRate: record.winRate,
      totalGames: record.totalGames
    }));

  // ELO progression
  const eloProgression = statistics.performanceTrends.map(trend => ({
    date: trend.date,
    elo: trend.eloRating,
    change: trend.eloChange
  }));

  return {
    recentPerformance: {
      last10Games: last10Stats,
      last30Days: last30DaysStats
    },
    strongestOpponents,
    weakestOpponents,
    eloProgression
  };
}