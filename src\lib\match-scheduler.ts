import { Match, MatchStatus, TournamentStatus, Game, GameStatus } from '@prisma/client';
import db from './db';
import { generateMove } from '@/ai/flows/generate-chess-move';
import { Chess } from 'chess.js';
import { defaultELOService } from './elo-service';
import { retryAIMove, retryDatabaseOperation, circuitBreakers } from './retry-utils';
import { TournamentEventEmitter } from './tournament-webhooks';

export interface MatchExecutionResult {
  matchId: string;
  gameId: string;
  result: '1-0' | '0-1' | '1/2-1/2' | '*';
  status: 'COMPLETED' | 'FAILED';
  whiteEloChange?: number;
  blackEloChange?: number;
  error?: string;
}

export interface ScheduledMatch extends Match {
  whiteProfile: {
    id: string;
    name: string;
    model: string;
    eloRating: number;
    gamesPlayed: number;
  };
  blackProfile: {
    id: string;
    name: string;
    model: string;
    eloRating: number;
    gamesPlayed: number;
  };
  tournament?: {
    id: string;
    name: string;
    status: TournamentStatus;
  };
}

/**
 * Match Scheduler Service for automated tournament progression
 */
export class MatchScheduler {
  private static runningMatches = new Set<string>();
  private static schedulerIntervals = new Map<string, NodeJS.Timeout>();

  /**
   * Start automated match execution for a tournament
   */
  static async startTournamentScheduler(tournamentId: string): Promise<void> {
    // Stop existing scheduler if running
    this.stopTournamentScheduler(tournamentId);

    const tournament = await db.tournament.findUnique({
      where: { id: tournamentId },
      select: { matchInterval: true, timeWindow: true, status: true },
    });

    if (!tournament || tournament.status !== TournamentStatus.ACTIVE) {
      throw new Error('Tournament not found or not active');
    }

    // Set up interval for match execution
    const intervalMs = tournament.matchInterval * 60 * 1000; // Convert minutes to milliseconds
    
    const schedulerInterval = setInterval(async () => {
      try {
        await this.executeNextScheduledMatch(tournamentId);
      } catch (error) {
        console.error(`Tournament ${tournamentId} scheduler error:`, error);
      }
    }, intervalMs);

    this.schedulerIntervals.set(tournamentId, schedulerInterval);
    
    // Execute first match immediately
    setTimeout(() => this.executeNextScheduledMatch(tournamentId), 1000);
  }

  /**
   * Stop automated match execution for a tournament
   */
  static stopTournamentScheduler(tournamentId: string): void {
    const interval = this.schedulerIntervals.get(tournamentId);
    if (interval) {
      clearInterval(interval);
      this.schedulerIntervals.delete(tournamentId);
    }
  }

  /**
   * Execute the next scheduled match for a tournament
   */
  static async executeNextScheduledMatch(tournamentId: string): Promise<MatchExecutionResult | null> {
    const tournament = await db.tournament.findUnique({
      where: { id: tournamentId },
      select: { status: true, timeWindow: true },
    });

    if (!tournament || tournament.status !== TournamentStatus.ACTIVE) {
      return null;
    }

    // Check if we're within the allowed time window
    if (!this.isWithinTimeWindow(tournament.timeWindow)) {
      return null;
    }

    // Get next available match
    const nextMatch = await this.getNextAvailableMatch(tournamentId);
    if (!nextMatch) {
      // Check if tournament is complete
      await this.checkTournamentCompletion(tournamentId);
      return null;
    }

    // Execute the match
    return await this.executeMatch(nextMatch);
  }

  /**
   * Get the next available match that can be executed
   */
  static async getNextAvailableMatch(tournamentId: string): Promise<ScheduledMatch | null> {
    const matches = await db.match.findMany({
      where: {
        tournamentId,
        status: MatchStatus.SCHEDULED,
        whiteProfileId: { not: 'TBD' },
        blackProfileId: { not: 'TBD' },
      },
      include: {
        whiteProfile: {
          select: {
            id: true,
            name: true,
            model: true,
            eloRating: true,
            gamesPlayed: true,
          },
        },
        blackProfile: {
          select: {
            id: true,
            name: true,
            model: true,
            eloRating: true,
            gamesPlayed: true,
          },
        },
        tournament: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
      },
      orderBy: [
        { round: 'asc' },
        { scheduledAt: 'asc' },
        { createdAt: 'asc' },
      ],
    });

    // Find first match where both participants are available
    for (const match of matches) {
      const isWhiteBusy = this.runningMatches.has(match.whiteProfileId);
      const isBlackBusy = this.runningMatches.has(match.blackProfileId);
      
      if (!isWhiteBusy && !isBlackBusy) {
        return match as ScheduledMatch;
      }
    }

    return null;
  }

  /**
   * Execute a single match
   */
  static async executeMatch(match: ScheduledMatch): Promise<MatchExecutionResult> {
    const matchId = match.id;
    
    // Mark participants as busy
    this.runningMatches.add(match.whiteProfileId);
    this.runningMatches.add(match.blackProfileId);

    try {
      // Update match status to IN_PROGRESS
      await db.match.update({
        where: { id: matchId },
        data: { 
          status: MatchStatus.IN_PROGRESS,
          scheduledAt: new Date(),
        },
      });

      // Create game record
      const game = await db.game.create({
        data: {
          white: match.whiteProfile.name,
          black: match.blackProfile.name,
          pgn: '',
          status: GameStatus.IN_PROGRESS,
        },
      });

      // Link match to game
      await db.match.update({
        where: { id: matchId },
        data: { gameId: game.id },
      });

      // Emit match started event
      if (match.tournament) {
        TournamentEventEmitter.matchStarted(match.tournament.id, {
          matchId: match.id,
          round: match.round || 'Unknown',
          whitePlayer: match.whiteProfile.name,
          blackPlayer: match.blackProfile.name,
        });
      }

      // Execute the chess game
      const gameResult = await this.runChessGame(
        game.id,
        match.whiteProfile.model,
        match.blackProfile.model
      );

      // Update ELO ratings using the ELO service
      const matchResult = {
        matchId,
        whiteProfileId: match.whiteProfileId,
        blackProfileId: match.blackProfileId,
        result: gameResult.result === '1-0' ? 'white' as const : 
                gameResult.result === '0-1' ? 'black' as const : 'draw' as const,
        gameId: game.id,
      };

      const eloUpdateResult = await defaultELOService.updateEloRatings(matchResult);

      const result: MatchExecutionResult = {
        matchId,
        gameId: game.id,
        result: gameResult.result,
        status: 'COMPLETED',
        whiteEloChange: eloUpdateResult.whiteProfile.eloChange,
        blackEloChange: eloUpdateResult.blackProfile.eloChange,
      };

      // Emit match completed event
      if (match.tournament) {
        TournamentEventEmitter.matchCompleted(match.tournament.id, {
          ...result,
          round: match.round || 'Unknown',
          whitePlayer: match.whiteProfile.name,
          blackPlayer: match.blackProfile.name,
        });
      }

      return result;

    } catch (error) {
      // Mark match as failed
      await db.match.update({
        where: { id: matchId },
        data: {
          status: MatchStatus.FAILED,
          completedAt: new Date(),
        },
      });

      const result: MatchExecutionResult = {
        matchId,
        gameId: '',
        result: '*',
        status: 'FAILED',
        error: error instanceof Error ? error.message : 'Unknown error',
      };

      // Emit match failed event
      if (match.tournament) {
        TournamentEventEmitter.matchFailed(match.tournament.id, {
          matchId: match.id,
          round: match.round || 'Unknown',
          whitePlayer: match.whiteProfile.name,
          blackPlayer: match.blackProfile.name,
          error: result.error || 'Unknown error',
        });
      }

      return result;

    } finally {
      // Release participants
      this.runningMatches.delete(match.whiteProfileId);
      this.runningMatches.delete(match.blackProfileId);
    }
  }

  /**
   * Run a chess game between two AI models
   */
  private static async runChessGame(
    gameId: string,
    whiteModel: string,
    blackModel: string
  ): Promise<{ result: '1-0' | '0-1' | '1/2-1/2' | '*'; pgn: string }> {
    const game = new Chess();
    let finalResult: '1-0' | '0-1' | '1/2-1/2' | '*' = '*';

    // Set game headers
    game.header('Event', 'Tournament Match');
    game.header('Site', 'Chess Duel Arena');
    game.header('Date', new Date().toISOString().split('T')[0]);
    game.header('Round', '1');
    game.header('White', whiteModel);
    game.header('Black', blackModel);

    const maxMoves = 200; // Prevent infinite games
    let moveCount = 0;

    try {
      while (!game.isGameOver() && moveCount < maxMoves) {
        const currentPlayer = game.turn() === 'w' ? 'white' : 'black';
        const currentModel = currentPlayer === 'white' ? whiteModel : blackModel;
        
        let moveSuccessful = false;
        let attempts = 0;
        const maxAttempts = 3;

        while (attempts < maxAttempts && !moveSuccessful) {
          attempts++;
          
          try {
            const legalMoves = game.moves({ verbose: false });
            
            // Use circuit breaker and retry logic for AI move generation
            const aiResult = await circuitBreakers.aiService.execute(async () => {
              return await retryAIMove(
                () => generateMove({
                  fen: game.fen(),
                  pgn: game.pgn(),
                  player: currentPlayer,
                  reasoningMode: false,
                  model: currentModel,
                  legalMoves: legalMoves,
                  isChess960: false,
                }),
                { player: currentPlayer, model: currentModel, attempt: attempts }
              );
            });

            if (aiResult && aiResult.move && game.move(aiResult.move)) {
              moveSuccessful = true;
              moveCount++;
              
              // Update game in database periodically with retry logic
              if (moveCount % 10 === 0) {
                await retryDatabaseOperation(
                  () => db.game.update({
                    where: { id: gameId },
                    data: { pgn: game.pgn() },
                  }),
                  { operation: 'update_game_pgn', table: 'game' }
                );
              }
            }
          } catch (error) {
            console.error(`Move generation error for ${currentModel} (attempt ${attempts}):`, error);
            
            // Add exponential backoff delay
            const delay = Math.min(1000 * Math.pow(2, attempts - 1), 5000);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }

        if (!moveSuccessful) {
          // If AI fails to make a move, opponent wins
          finalResult = currentPlayer === 'white' ? '0-1' : '1-0';
          break;
        }
      }

      // Determine final result
      if (finalResult === '*') {
        if (game.isCheckmate()) {
          finalResult = game.turn() === 'b' ? '1-0' : '0-1';
        } else if (game.isDraw() || game.isStalemate() || 
                   game.isThreefoldRepetition() || game.isInsufficientMaterial()) {
          finalResult = '1/2-1/2';
        } else if (moveCount >= maxMoves) {
          finalResult = '1/2-1/2'; // Draw by move limit
        }
      }

      game.header('Result', finalResult);
      const finalPgn = game.pgn();

      // Update final game state with retry logic
      await retryDatabaseOperation(
        () => db.game.update({
          where: { id: gameId },
          data: {
            pgn: finalPgn,
            result: finalResult,
            status: GameStatus.COMPLETED,
          },
        }),
        { operation: 'update_final_game_state', table: 'game' }
      );

      return { result: finalResult, pgn: finalPgn };

    } catch (error) {
      // Update game as failed with retry logic
      try {
        await retryDatabaseOperation(
          () => db.game.update({
            where: { id: gameId },
            data: {
              status: GameStatus.FAILED,
              result: '*',
            },
          }),
          { operation: 'mark_game_failed', table: 'game' }
        );
      } catch (dbError) {
        console.error('Failed to mark game as failed in database:', dbError);
      }
      
      throw error;
    }
  }

  /**
   * Check if current time is within tournament time window
   */
  private static isWithinTimeWindow(timeWindow: string): boolean {
    if (timeWindow === '00:00-23:59') return true;

    const [startTime, endTime] = timeWindow.split('-');
    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    
    const [startHour, startMin] = startTime.split(':').map(Number);
    const [endHour, endMin] = endTime.split(':').map(Number);
    
    const startMinutes = startHour * 60 + startMin;
    const endMinutes = endHour * 60 + endMin;

    return currentTime >= startMinutes && currentTime <= endMinutes;
  }

  /**
   * Check if tournament is complete and update status
   */
  private static async checkTournamentCompletion(tournamentId: string): Promise<void> {
    const pendingMatches = await db.match.count({
      where: {
        tournamentId,
        status: { in: [MatchStatus.SCHEDULED, MatchStatus.IN_PROGRESS] },
      },
    });

    if (pendingMatches === 0) {
      const tournament = await db.tournament.update({
        where: { id: tournamentId },
        data: { status: TournamentStatus.COMPLETED },
        include: {
          participants: {
            include: {
              profile: {
                select: { name: true },
              },
            },
          },
          matches: {
            where: { status: MatchStatus.COMPLETED },
          },
        },
      });
      
      // Emit tournament completed event
      TournamentEventEmitter.tournamentCompleted(tournamentId, {
        name: tournament.name,
        totalMatches: tournament.matches.length,
        // TODO: Determine winner based on tournament format
      });
      
      // Stop the scheduler
      this.stopTournamentScheduler(tournamentId);
    }
  }

  /**
   * Get match execution status for a tournament
   */
  static async getTournamentProgress(tournamentId: string): Promise<{
    totalMatches: number;
    completedMatches: number;
    inProgressMatches: number;
    scheduledMatches: number;
    failedMatches: number;
  }> {
    const [total, completed, inProgress, scheduled, failed] = await Promise.all([
      db.match.count({ where: { tournamentId } }),
      db.match.count({ where: { tournamentId, status: MatchStatus.COMPLETED } }),
      db.match.count({ where: { tournamentId, status: MatchStatus.IN_PROGRESS } }),
      db.match.count({ where: { tournamentId, status: MatchStatus.SCHEDULED } }),
      db.match.count({ where: { tournamentId, status: MatchStatus.FAILED } }),
    ]);

    return {
      totalMatches: total,
      completedMatches: completed,
      inProgressMatches: inProgress,
      scheduledMatches: scheduled,
      failedMatches: failed,
    };
  }

  /**
   * Retry failed matches
   */
  static async retryFailedMatches(tournamentId: string): Promise<number> {
    const failedMatches = await db.match.findMany({
      where: {
        tournamentId,
        status: MatchStatus.FAILED,
      },
    });

    await db.match.updateMany({
      where: {
        tournamentId,
        status: MatchStatus.FAILED,
      },
      data: {
        status: MatchStatus.SCHEDULED,
        completedAt: null,
        whiteEloChange: null,
        blackEloChange: null,
      },
    });

    return failedMatches.length;
  }

  /**
   * Get currently running matches
   */
  static getRunningMatches(): string[] {
    return Array.from(this.runningMatches);
  }

  /**
   * Force stop a running match (emergency use)
   */
  static forceStopMatch(profileId: string): void {
    this.runningMatches.delete(profileId);
  }
}