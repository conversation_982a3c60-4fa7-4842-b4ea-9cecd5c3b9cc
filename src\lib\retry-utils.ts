/**
 * Retry utility functions for handling failed operations
 */

export interface RetryOptions {
  maxAttempts: number;
  baseDelay: number; // milliseconds
  maxDelay: number; // milliseconds
  backoffMultiplier: number;
  retryCondition?: (error: any) => boolean;
}

export interface RetryResult<T> {
  success: boolean;
  result?: T;
  error?: Error;
  attempts: number;
  totalTime: number;
}

/**
 * Default retry options for different operation types
 */
export const DEFAULT_RETRY_OPTIONS: { [key: string]: RetryOptions } = {
  aiMove: {
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 5000,
    backoffMultiplier: 2,
    retryCondition: (error) => {
      // Retry on network errors, timeouts, but not on invalid moves
      return !error.message?.includes('Invalid move') && 
             !error.message?.includes('Illegal move');
    },
  },
  databaseOperation: {
    maxAttempts: 5,
    baseDelay: 500,
    maxDelay: 3000,
    backoffMultiplier: 1.5,
    retryCondition: (error) => {
      // Retry on connection errors, timeouts, but not on constraint violations
      return error.code !== 'P2002' && // Unique constraint violation
             error.code !== 'P2003' && // Foreign key constraint violation
             error.code !== 'P2025';   // Record not found
    },
  },
  matchExecution: {
    maxAttempts: 2,
    baseDelay: 2000,
    maxDelay: 10000,
    backoffMultiplier: 3,
    retryCondition: (error) => {
      // Only retry on system errors, not game logic errors
      return !error.message?.includes('checkmate') &&
             !error.message?.includes('stalemate') &&
             !error.message?.includes('draw');
    },
  },
};

/**
 * Execute a function with exponential backoff retry logic
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  options: RetryOptions = DEFAULT_RETRY_OPTIONS.aiMove
): Promise<RetryResult<T>> {
  const startTime = Date.now();
  let lastError: Error | null = null;
  let actualAttempts = 0;
  
  for (let attempt = 1; attempt <= options.maxAttempts; attempt++) {
    actualAttempts = attempt;
    try {
      const result = await operation();
      return {
        success: true,
        result,
        attempts: attempt,
        totalTime: Date.now() - startTime,
      };
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      // Check if we should retry this error
      if (options.retryCondition && !options.retryCondition(lastError)) {
        break;
      }
      
      // Don't wait after the last attempt
      if (attempt < options.maxAttempts) {
        const delay = Math.min(
          options.baseDelay * Math.pow(options.backoffMultiplier, attempt - 1),
          options.maxDelay
        );
        
        console.log(`Attempt ${attempt} failed, retrying in ${delay}ms:`, lastError.message);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  return {
    success: false,
    error: lastError || new Error('Unknown error'),
    attempts: actualAttempts,
    totalTime: Date.now() - startTime,
  };
}

/**
 * Retry wrapper specifically for AI move generation
 */
export async function retryAIMove<T>(
  operation: () => Promise<T>,
  context: { player: string; model: string; attempt?: number } = { player: 'unknown', model: 'unknown' }
): Promise<T> {
  const result = await withRetry(operation, {
    ...DEFAULT_RETRY_OPTIONS.aiMove,
    retryCondition: (error) => {
      // Log specific AI move failures
      console.warn(`AI move generation failed for ${context.model} (${context.player}):`, error.message);
      
      // Don't retry on clearly invalid moves or model errors
      if (error.message?.includes('Invalid move') || 
          error.message?.includes('Illegal move') ||
          error.message?.includes('Model not found')) {
        return false;
      }
      
      return true;
    },
  });
  
  if (!result.success) {
    throw new Error(`AI move generation failed after ${result.attempts} attempts: ${result.error?.message}`);
  }
  
  return result.result!;
}

/**
 * Retry wrapper for database operations
 */
export async function retryDatabaseOperation<T>(
  operation: () => Promise<T>,
  context: { operation: string; table?: string } = { operation: 'unknown' }
): Promise<T> {
  const result = await withRetry(operation, {
    ...DEFAULT_RETRY_OPTIONS.databaseOperation,
    retryCondition: (error) => {
      console.warn(`Database operation failed (${context.operation}):`, error.message);
      
      // Check for specific Prisma error codes that shouldn't be retried
      if (error.code === 'P2002') {
        console.log('Unique constraint violation - not retrying');
        return false;
      }
      
      if (error.code === 'P2025') {
        console.log('Record not found - not retrying');
        return false;
      }
      
      return true;
    },
  });
  
  if (!result.success) {
    throw new Error(`Database operation failed after ${result.attempts} attempts: ${result.error?.message}`);
  }
  
  return result.result!;
}

/**
 * Circuit breaker pattern for preventing cascading failures
 */
export class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'closed' | 'open' | 'half-open' = 'closed';
  
  constructor(
    private failureThreshold: number = 5,
    private recoveryTimeout: number = 60000, // 1 minute
    private successThreshold: number = 2
  ) {}
  
  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'open') {
      if (Date.now() - this.lastFailureTime > this.recoveryTimeout) {
        this.state = 'half-open';
        console.log('Circuit breaker moving to half-open state');
      } else {
        throw new Error('Circuit breaker is open - operation blocked');
      }
    }
    
    try {
      const result = await operation();
      
      if (this.state === 'half-open') {
        this.failures = Math.max(0, this.failures - 1);
        if (this.failures === 0) {
          this.state = 'closed';
          console.log('Circuit breaker closed - service recovered');
        }
      } else {
        this.failures = Math.max(0, this.failures - 1);
      }
      
      return result;
    } catch (error) {
      this.failures++;
      this.lastFailureTime = Date.now();
      
      if (this.failures >= this.failureThreshold) {
        this.state = 'open';
        console.error(`Circuit breaker opened after ${this.failures} failures`);
      }
      
      throw error;
    }
  }
  
  getState(): { state: string; failures: number; lastFailureTime: number } {
    return {
      state: this.state,
      failures: this.failures,
      lastFailureTime: this.lastFailureTime,
    };
  }
  
  reset(): void {
    this.failures = 0;
    this.lastFailureTime = 0;
    this.state = 'closed';
    console.log('Circuit breaker manually reset');
  }
}

/**
 * Global circuit breakers for different services
 */
export const circuitBreakers = {
  aiService: new CircuitBreaker(3, 30000, 1), // More sensitive for AI calls
  database: new CircuitBreaker(5, 60000, 2),
  matchExecution: new CircuitBreaker(2, 120000, 1), // Very sensitive for match execution
};

/**
 * Utility function to check if an error is retryable
 */
export function isRetryableError(error: any): boolean {
  // Network errors
  if (error.code === 'ECONNRESET' || 
      error.code === 'ENOTFOUND' || 
      error.code === 'ETIMEDOUT') {
    return true;
  }
  
  // HTTP errors that might be temporary
  if (error.status >= 500 && error.status < 600) {
    return true;
  }
  
  // Rate limiting
  if (error.status === 429) {
    return true;
  }
  
  // Specific error messages that indicate temporary issues
  const retryableMessages = [
    'timeout',
    'connection',
    'network',
    'temporary',
    'unavailable',
    'overloaded',
  ];
  
  const errorMessage = error.message?.toLowerCase() || '';
  return retryableMessages.some(msg => errorMessage.includes(msg));
}

/**
 * Create a delay with jitter to prevent thundering herd
 */
export function createJitteredDelay(baseDelay: number, jitterFactor: number = 0.1): number {
  const jitter = baseDelay * jitterFactor * (Math.random() - 0.5) * 2;
  return Math.max(0, baseDelay + jitter);
}