import { PrismaClient, Tournament, TournamentFormat, TournamentStatus, MatchStatus, TournamentParticipant, Match } from '@prisma/client';
import db from './db';

export interface CreateTournamentInput {
  name: string;
  format: TournamentFormat;
  participantIds: string[];
  scheduledStart?: Date;
  matchInterval?: number;
  timeWindow?: string;
}

export interface TournamentWithDetails extends Tournament {
  participants: (TournamentParticipant & {
    profile: {
      id: string;
      name: string;
      model: string;
      eloRating: number;
    };
  })[];
  matches: (Match & {
    whiteProfile: {
      id: string;
      name: string;
      model: string;
    };
    blackProfile: {
      id: string;
      name: string;
      model: string;
    };
  })[];
}

export interface BracketMatch {
  id?: string;
  round: string;
  whiteProfileId: string;
  blackProfileId: string;
  whiteProfile?: {
    id: string;
    name: string;
    model: string;
  };
  blackProfile?: {
    id: string;
    name: string;
    model: string;
  };
  status: MatchStatus;
  scheduledAt?: Date;
}

/**
 * Tournament CRUD Operations
 */
export class TournamentService {
  /**
   * Create a new tournament with participants and generate initial bracket
   */
  static async createTournament(input: CreateTournamentInput): Promise<TournamentWithDetails> {
    if (input.participantIds.length < 2) {
      throw new Error('Tournament must have at least 2 participants');
    }

    return await db.$transaction(async (tx) => {
      // Create tournament
      const tournament = await tx.tournament.create({
        data: {
          name: input.name,
          format: input.format,
          scheduledStart: input.scheduledStart,
          matchInterval: input.matchInterval || 30,
          timeWindow: input.timeWindow || '00:00-23:59',
          status: TournamentStatus.DRAFT,
        },
      });

      // Add participants with seeding
      const participants = await Promise.all(
        input.participantIds.map((profileId, index) =>
          tx.tournamentParticipant.create({
            data: {
              tournamentId: tournament.id,
              profileId,
              seed: index + 1,
            },
            include: {
              profile: {
                select: {
                  id: true,
                  name: true,
                  model: true,
                  eloRating: true,
                },
              },
            },
          })
        )
      );

      // Generate bracket matches
      const bracketMatches = this.generateBracket(tournament.format, participants);
      
      const matches = await Promise.all(
        bracketMatches.map((match) =>
          tx.match.create({
            data: {
              tournamentId: tournament.id,
              whiteProfileId: match.whiteProfileId,
              blackProfileId: match.blackProfileId,
              round: match.round,
              status: MatchStatus.SCHEDULED,
              scheduledAt: match.scheduledAt,
            },
            include: {
              whiteProfile: {
                select: {
                  id: true,
                  name: true,
                  model: true,
                },
              },
              blackProfile: {
                select: {
                  id: true,
                  name: true,
                  model: true,
                },
              },
            },
          })
        )
      );

      return {
        ...tournament,
        participants,
        matches,
      };
    }, {
      timeout: 15000, // 15 seconds timeout
    });
  }

  /**
   * Get tournament by ID with full details
   */
  static async getTournamentById(id: string): Promise<TournamentWithDetails | null> {
    return await db.tournament.findUnique({
      where: { id },
      include: {
        participants: {
          include: {
            profile: {
              select: {
                id: true,
                name: true,
                model: true,
                eloRating: true,
              },
            },
          },
          orderBy: { seed: 'asc' },
        },
        matches: {
          include: {
            whiteProfile: {
              select: {
                id: true,
                name: true,
                model: true,
              },
            },
            blackProfile: {
              select: {
                id: true,
                name: true,
                model: true,
              },
            },
          },
          orderBy: [{ round: 'asc' }, { createdAt: 'asc' }],
        },
      },
    });
  }

  /**
   * Get all tournaments with basic info
   */
  static async getAllTournaments(): Promise<TournamentWithDetails[]> {
    return await db.tournament.findMany({
      include: {
        participants: {
          include: {
            profile: {
              select: {
                id: true,
                name: true,
                model: true,
                eloRating: true,
              },
            },
          },
          orderBy: { seed: 'asc' },
        },
        matches: {
          include: {
            whiteProfile: {
              select: {
                id: true,
                name: true,
                model: true,
              },
            },
            blackProfile: {
              select: {
                id: true,
                name: true,
                model: true,
              },
            },
          },
          orderBy: [{ round: 'asc' }, { createdAt: 'asc' }],
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * Update tournament status
   */
  static async updateTournamentStatus(id: string, status: TournamentStatus): Promise<Tournament> {
    return await db.tournament.update({
      where: { id },
      data: { status, updatedAt: new Date() },
    });
  }

  /**
   * Delete tournament and all related data
   */
  static async deleteTournament(id: string): Promise<void> {
    await db.tournament.delete({
      where: { id },
    });
  }

  /**
   * Generate bracket matches based on tournament format
   */
  static generateBracket(
    format: TournamentFormat,
    participants: (TournamentParticipant & {
      profile: {
        id: string;
        name: string;
        model: string;
        eloRating: number;
      };
    })[]
  ): BracketMatch[] {
    switch (format) {
      case TournamentFormat.ROUND_ROBIN:
        return this.generateRoundRobinBracket(participants);
      case TournamentFormat.SINGLE_ELIMINATION:
        return this.generateSingleEliminationBracket(participants);
      case TournamentFormat.DOUBLE_ELIMINATION:
        return this.generateDoubleEliminationBracket(participants);
      default:
        throw new Error(`Unsupported tournament format: ${format}`);
    }
  }

  /**
   * Generate round-robin bracket (everyone plays everyone)
   */
  private static generateRoundRobinBracket(
    participants: (TournamentParticipant & {
      profile: {
        id: string;
        name: string;
        model: string;
        eloRating: number;
      };
    })[]
  ): BracketMatch[] {
    const matches: BracketMatch[] = [];
    const n = participants.length;

    for (let i = 0; i < n; i++) {
      for (let j = i + 1; j < n; j++) {
        const round = Math.floor(matches.length / Math.floor(n / 2)) + 1;
        matches.push({
          round: `Round ${round}`,
          whiteProfileId: participants[i].profileId,
          blackProfileId: participants[j].profileId,
          status: MatchStatus.SCHEDULED,
        });
      }
    }

    return matches;
  }

  /**
   * Generate single elimination bracket
   */
  private static generateSingleEliminationBracket(
    participants: (TournamentParticipant & {
      profile: {
        id: string;
        name: string;
        model: string;
        eloRating: number;
      };
    })[]
  ): BracketMatch[] {
    const matches: BracketMatch[] = [];
    const n = participants.length;
    
    // First round matches - pair up participants
    for (let i = 0; i < n; i += 2) {
      if (i + 1 < n) {
        matches.push({
          round: 'Round 1',
          whiteProfileId: participants[i].profileId,
          blackProfileId: participants[i + 1].profileId,
          status: MatchStatus.SCHEDULED,
        });
      }
    }

    // Generate placeholder matches for subsequent rounds
    let currentRoundMatches = Math.floor(n / 2);
    let roundNumber = 2;
    
    while (currentRoundMatches > 1) {
      const nextRoundMatches = Math.floor(currentRoundMatches / 2);
      const roundName = nextRoundMatches === 1 ? 'Semifinal' : `Round ${roundNumber}`;
      
      for (let i = 0; i < nextRoundMatches; i++) {
        matches.push({
          round: roundName,
          whiteProfileId: 'TBD', // To be determined from previous round
          blackProfileId: 'TBD',
          status: MatchStatus.SCHEDULED,
        });
      }
      
      currentRoundMatches = nextRoundMatches;
      roundNumber++;
    }

    // Final match
    if (n > 2) {
      matches.push({
        round: 'Final',
        whiteProfileId: 'TBD',
        blackProfileId: 'TBD',
        status: MatchStatus.SCHEDULED,
      });
    }

    return matches;
  }

  /**
   * Generate double elimination bracket (winners and losers brackets)
   */
  private static generateDoubleEliminationBracket(
    participants: (TournamentParticipant & {
      profile: {
        id: string;
        name: string;
        model: string;
        eloRating: number;
      };
    })[]
  ): BracketMatch[] {
    const matches: BracketMatch[] = [];
    const n = participants.length;
    
    // Winners bracket first round
    for (let i = 0; i < n; i += 2) {
      if (i + 1 < n) {
        matches.push({
          round: 'WB Round 1',
          whiteProfileId: participants[i].profileId,
          blackProfileId: participants[i + 1].profileId,
          status: MatchStatus.SCHEDULED,
        });
      }
    }

    // Generate subsequent winners bracket rounds
    let wbMatches = Math.floor(matches.length / 2);
    let wbRound = 2;
    
    while (wbMatches > 1) {
      for (let i = 0; i < wbMatches; i++) {
        matches.push({
          round: `WB Round ${wbRound}`,
          whiteProfileId: 'TBD',
          blackProfileId: 'TBD',
          status: MatchStatus.SCHEDULED,
        });
      }
      wbMatches = Math.floor(wbMatches / 2);
      wbRound++;
    }

    // Winners bracket final
    matches.push({
      round: 'WB Final',
      whiteProfileId: 'TBD',
      blackProfileId: 'TBD',
      status: MatchStatus.SCHEDULED,
    });

    // Losers bracket (simplified - would need more complex logic for full implementation)
    const lbRounds = Math.ceil(Math.log2(n)) * 2 - 1;
    for (let round = 1; round <= lbRounds; round++) {
      matches.push({
        round: `LB Round ${round}`,
        whiteProfileId: 'TBD',
        blackProfileId: 'TBD',
        status: MatchStatus.SCHEDULED,
      });
    }

    // Grand final
    matches.push({
      round: 'Grand Final',
      whiteProfileId: 'TBD',
      blackProfileId: 'TBD',
      status: MatchStatus.SCHEDULED,
    });

    return matches;
  }

  /**
   * Check if participants are available for matches
   */
  static async checkParticipantAvailability(profileIds: string[]): Promise<{ [profileId: string]: boolean }> {
    const activeMatches = await db.match.findMany({
      where: {
        OR: [
          { whiteProfileId: { in: profileIds } },
          { blackProfileId: { in: profileIds } },
        ],
        status: MatchStatus.IN_PROGRESS,
      },
      select: {
        whiteProfileId: true,
        blackProfileId: true,
      },
    });

    const busyProfiles = new Set<string>();
    activeMatches.forEach(match => {
      busyProfiles.add(match.whiteProfileId);
      busyProfiles.add(match.blackProfileId);
    });

    const availability: { [profileId: string]: boolean } = {};
    profileIds.forEach(id => {
      availability[id] = !busyProfiles.has(id);
    });

    return availability;
  }

  /**
   * Get next scheduled matches for a tournament
   */
  static async getNextScheduledMatches(tournamentId: string, limit: number = 5): Promise<Match[]> {
    return await db.match.findMany({
      where: {
        tournamentId,
        status: MatchStatus.SCHEDULED,
      },
      include: {
        whiteProfile: {
          select: {
            id: true,
            name: true,
            model: true,
          },
        },
        blackProfile: {
          select: {
            id: true,
            name: true,
            model: true,
          },
        },
      },
      orderBy: [
        { round: 'asc' },
        { scheduledAt: 'asc' },
        { createdAt: 'asc' },
      ],
      take: limit,
    });
  }

  /**
   * Update match status
   */
  static async updateMatchStatus(matchId: string, status: MatchStatus): Promise<Match> {
    return await db.match.update({
      where: { id: matchId },
      data: { 
        status,
        ...(status === MatchStatus.IN_PROGRESS && { scheduledAt: new Date() }),
        ...(status === MatchStatus.COMPLETED && { completedAt: new Date() }),
      },
    });
  }

  /**
   * Get tournament standings (for round-robin format)
   */
  static async getTournamentStandings(tournamentId: string): Promise<{
    profileId: string;
    profile: { name: string; model: string; eloRating: number };
    wins: number;
    losses: number;
    draws: number;
    points: number;
  }[]> {
    const tournament = await this.getTournamentById(tournamentId);
    if (!tournament) {
      throw new Error('Tournament not found');
    }

    const standings = tournament.participants.map(participant => {
      const profileMatches = tournament.matches.filter(match => 
        (match.whiteProfileId === participant.profileId || match.blackProfileId === participant.profileId) &&
        match.status === MatchStatus.COMPLETED
      );

      let wins = 0;
      let losses = 0;
      let draws = 0;

      profileMatches.forEach(match => {
        // This would need to be determined from the actual game result
        // For now, we'll use a placeholder logic
        const isWhite = match.whiteProfileId === participant.profileId;
        // TODO: Get actual game result from match.game.result
        // For now, assume random results for demonstration
      });

      return {
        profileId: participant.profileId,
        profile: participant.profile,
        wins,
        losses,
        draws,
        points: wins + (draws * 0.5),
      };
    });

    return standings.sort((a, b) => b.points - a.points);
  }
}