import { EventEmitter } from 'events';
import { MatchExecutionResult } from './match-scheduler';
import { TournamentStatus, MatchStatus } from '@prisma/client';

export interface TournamentEvent {
  type: 'tournament_started' | 'tournament_paused' | 'tournament_resumed' | 'tournament_completed' | 
        'match_started' | 'match_completed' | 'match_failed' | 'round_completed';
  tournamentId: string;
  timestamp: Date;
  data: any;
}

export interface WebhookSubscription {
  id: string;
  tournamentId: string;
  url?: string;
  callback?: (event: TournamentEvent) => void;
  events: TournamentEvent['type'][];
  active: boolean;
  createdAt: Date;
}

/**
 * Tournament Webhook System for real-time updates
 */
export class TournamentWebhooks {
  private static eventEmitter = new EventEmitter();
  private static subscriptions = new Map<string, WebhookSubscription>();
  private static eventHistory = new Map<string, TournamentEvent[]>();

  /**
   * Subscribe to tournament events
   */
  static subscribe(subscription: Omit<WebhookSubscription, 'id' | 'createdAt'>): string {
    const id = `sub_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const fullSubscription: WebhookSubscription = {
      ...subscription,
      id,
      createdAt: new Date(),
    };

    this.subscriptions.set(id, fullSubscription);

    // Set up event listeners for this subscription
    subscription.events.forEach(eventType => {
      this.eventEmitter.on(`${subscription.tournamentId}:${eventType}`, (event: TournamentEvent) => {
        this.handleEvent(id, event);
      });
    });

    console.log(`Created webhook subscription ${id} for tournament ${subscription.tournamentId}`);
    return id;
  }

  /**
   * Unsubscribe from tournament events
   */
  static unsubscribe(subscriptionId: string): boolean {
    const subscription = this.subscriptions.get(subscriptionId);
    if (!subscription) {
      return false;
    }

    // Remove event listeners
    subscription.events.forEach(eventType => {
      this.eventEmitter.removeAllListeners(`${subscription.tournamentId}:${eventType}`);
    });

    this.subscriptions.delete(subscriptionId);
    console.log(`Removed webhook subscription ${subscriptionId}`);
    return true;
  }

  /**
   * Emit a tournament event
   */
  static emitEvent(event: TournamentEvent): void {
    // Store event in history
    const history = this.eventHistory.get(event.tournamentId) || [];
    history.push(event);
    
    // Keep only last 100 events per tournament
    if (history.length > 100) {
      history.shift();
    }
    
    this.eventHistory.set(event.tournamentId, history);

    // Emit to subscribers
    this.eventEmitter.emit(`${event.tournamentId}:${event.type}`, event);
    
    console.log(`Emitted tournament event: ${event.type} for tournament ${event.tournamentId}`);
  }

  /**
   * Handle event delivery to a specific subscription
   */
  private static async handleEvent(subscriptionId: string, event: TournamentEvent): Promise<void> {
    const subscription = this.subscriptions.get(subscriptionId);
    if (!subscription || !subscription.active) {
      return;
    }

    try {
      if (subscription.callback) {
        // Call callback function
        subscription.callback(event);
      } else if (subscription.url) {
        // Send HTTP webhook
        await this.sendWebhook(subscription.url, event);
      }
    } catch (error) {
      console.error(`Failed to deliver webhook for subscription ${subscriptionId}:`, error);
      
      // Optionally disable subscription after repeated failures
      // This could be enhanced with retry logic and failure counting
    }
  }

  /**
   * Send HTTP webhook
   */
  private static async sendWebhook(url: string, event: TournamentEvent): Promise<void> {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Chess-Duel-Arena-Webhooks/1.0',
      },
      body: JSON.stringify({
        event: event.type,
        tournament_id: event.tournamentId,
        timestamp: event.timestamp.toISOString(),
        data: event.data,
      }),
    });

    if (!response.ok) {
      throw new Error(`Webhook delivery failed: ${response.status} ${response.statusText}`);
    }
  }

  /**
   * Get event history for a tournament
   */
  static getEventHistory(tournamentId: string, limit: number = 50): TournamentEvent[] {
    const history = this.eventHistory.get(tournamentId) || [];
    return history.slice(-limit);
  }

  /**
   * Get all active subscriptions for a tournament
   */
  static getSubscriptions(tournamentId: string): WebhookSubscription[] {
    return Array.from(this.subscriptions.values())
      .filter(sub => sub.tournamentId === tournamentId && sub.active);
  }

  /**
   * Get subscription by ID
   */
  static getSubscription(subscriptionId: string): WebhookSubscription | null {
    return this.subscriptions.get(subscriptionId) || null;
  }

  /**
   * Update subscription status
   */
  static updateSubscription(subscriptionId: string, updates: Partial<WebhookSubscription>): boolean {
    const subscription = this.subscriptions.get(subscriptionId);
    if (!subscription) {
      return false;
    }

    Object.assign(subscription, updates);
    this.subscriptions.set(subscriptionId, subscription);
    return true;
  }

  /**
   * Clean up old event history
   */
  static cleanupEventHistory(maxAge: number = 24 * 60 * 60 * 1000): void {
    const cutoff = new Date(Date.now() - maxAge);
    
    for (const [tournamentId, events] of this.eventHistory.entries()) {
      const filteredEvents = events.filter(event => event.timestamp > cutoff);
      
      if (filteredEvents.length === 0) {
        this.eventHistory.delete(tournamentId);
      } else {
        this.eventHistory.set(tournamentId, filteredEvents);
      }
    }
  }

  /**
   * Get system statistics
   */
  static getStats(): {
    totalSubscriptions: number;
    activeSubscriptions: number;
    tournamentsWithSubscriptions: number;
    totalEvents: number;
  } {
    const subscriptions = Array.from(this.subscriptions.values());
    const tournamentIds = new Set(subscriptions.map(sub => sub.tournamentId));
    const totalEvents = Array.from(this.eventHistory.values())
      .reduce((sum, events) => sum + events.length, 0);

    return {
      totalSubscriptions: subscriptions.length,
      activeSubscriptions: subscriptions.filter(sub => sub.active).length,
      tournamentsWithSubscriptions: tournamentIds.size,
      totalEvents,
    };
  }
}

/**
 * Helper functions to emit specific tournament events
 */
export class TournamentEventEmitter {
  static tournamentStarted(tournamentId: string, data: { name: string; participantCount: number }): void {
    TournamentWebhooks.emitEvent({
      type: 'tournament_started',
      tournamentId,
      timestamp: new Date(),
      data,
    });
  }

  static tournamentPaused(tournamentId: string, data: { name: string }): void {
    TournamentWebhooks.emitEvent({
      type: 'tournament_paused',
      tournamentId,
      timestamp: new Date(),
      data,
    });
  }

  static tournamentResumed(tournamentId: string, data: { name: string }): void {
    TournamentWebhooks.emitEvent({
      type: 'tournament_resumed',
      tournamentId,
      timestamp: new Date(),
      data,
    });
  }

  static tournamentCompleted(tournamentId: string, data: { name: string; totalMatches: number; winner?: string }): void {
    TournamentWebhooks.emitEvent({
      type: 'tournament_completed',
      tournamentId,
      timestamp: new Date(),
      data,
    });
  }

  static matchStarted(tournamentId: string, data: { matchId: string; round: string; whitePlayer: string; blackPlayer: string }): void {
    TournamentWebhooks.emitEvent({
      type: 'match_started',
      tournamentId,
      timestamp: new Date(),
      data,
    });
  }

  static matchCompleted(tournamentId: string, data: MatchExecutionResult & { round: string; whitePlayer: string; blackPlayer: string }): void {
    TournamentWebhooks.emitEvent({
      type: 'match_completed',
      tournamentId,
      timestamp: new Date(),
      data,
    });
  }

  static matchFailed(tournamentId: string, data: { matchId: string; round: string; whitePlayer: string; blackPlayer: string; error: string }): void {
    TournamentWebhooks.emitEvent({
      type: 'match_failed',
      tournamentId,
      timestamp: new Date(),
      data,
    });
  }

  static roundCompleted(tournamentId: string, data: { round: string; completedMatches: number; nextRound?: string }): void {
    TournamentWebhooks.emitEvent({
      type: 'round_completed',
      tournamentId,
      timestamp: new Date(),
      data,
    });
  }
}

// Clean up event history periodically
if (typeof setInterval !== 'undefined') {
  setInterval(() => {
    TournamentWebhooks.cleanupEventHistory();
  }, 60 * 60 * 1000); // Every hour
}