import { describe, it, expect, vi, beforeEach } from 'vitest'
import { GET, POST } from '@/app/api/profiles/route'
import { NextRequest } from 'next/server'

// Mock Prisma
const mockPrisma = {
  lLMProfile: {
    findMany: vi.fn(),
    create: vi.fn(),
    findUnique: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
  },
}

vi.mock('@/lib/db', () => ({
  default: mockPrisma,
}))

describe('/api/profiles', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('GET /api/profiles', () => {
    it('should return all profiles', async () => {
      const mockProfiles = [
        {
          id: '1',
          name: 'GPT-4',
          model: 'gpt-4',
          eloRating: 1500,
          gamesPlayed: 0,
          wins: 0,
          losses: 0,
          draws: 0,
        },
      ]

      mockPrisma.lLMProfile.findMany.mockResolvedValue(mockProfiles)

      const response = await GET()
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data).toEqual(mockProfiles)
      expect(mockPrisma.lLMProfile.findMany).toHaveBeenCalledWith({
        orderBy: { createdAt: 'desc' },
      })
    })

    it('should handle database error', async () => {
      mockPrisma.lLMProfile.findMany.mockRejectedValue(new Error('Database error'))

      const response = await GET()
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Failed to fetch profiles')
    })
  })

  describe('POST /api/profiles', () => {
    it('should create a new profile', async () => {
      const newProfile = {
        name: 'Claude',
        model: 'claude-3',
        eloRating: 1400,
      }

      const createdProfile = {
        id: '2',
        ...newProfile,
        gamesPlayed: 0,
        wins: 0,
        losses: 0,
        draws: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockPrisma.lLMProfile.create.mockResolvedValue(createdProfile)

      const request = new NextRequest('http://localhost:3000/api/profiles', {
        method: 'POST',
        body: JSON.stringify(newProfile),
        headers: { 'Content-Type': 'application/json' },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(201)
      expect(data).toEqual(createdProfile)
      expect(mockPrisma.lLMProfile.create).toHaveBeenCalledWith({
        data: {
          name: newProfile.name,
          model: newProfile.model,
          eloRating: newProfile.eloRating,
        },
      })
    })

    it('should validate required fields', async () => {
      const invalidProfile = {
        model: 'gpt-4',
        // missing name
      }

      const request = new NextRequest('http://localhost:3000/api/profiles', {
        method: 'POST',
        body: JSON.stringify(invalidProfile),
        headers: { 'Content-Type': 'application/json' },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toContain('Name is required')
    })

    it('should handle database error during creation', async () => {
      const newProfile = {
        name: 'Claude',
        model: 'claude-3',
      }

      mockPrisma.lLMProfile.create.mockRejectedValue(new Error('Database error'))

      const request = new NextRequest('http://localhost:3000/api/profiles', {
        method: 'POST',
        body: JSON.stringify(newProfile),
        headers: { 'Content-Type': 'application/json' },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(500)
      expect(data.error).toBe('Failed to create profile')
    })

    it('should handle invalid JSON', async () => {
      const request = new NextRequest('http://localhost:3000/api/profiles', {
        method: 'POST',
        body: 'invalid json',
        headers: { 'Content-Type': 'application/json' },
      })

      const response = await POST(request)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toBe('Invalid JSON')
    })

    it('should set default ELO rating', async () => {
      const newProfile = {
        name: 'Claude',
        model: 'claude-3',
        // no eloRating provided
      }

      const createdProfile = {
        id: '2',
        ...newProfile,
        eloRating: 1500, // default value
        gamesPlayed: 0,
        wins: 0,
        losses: 0,
        draws: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      mockPrisma.lLMProfile.create.mockResolvedValue(createdProfile)

      const request = new NextRequest('http://localhost:3000/api/profiles', {
        method: 'POST',
        body: JSON.stringify(newProfile),
        headers: { 'Content-Type': 'application/json' },
      })

      const response = await POST(request)

      expect(response.status).toBe(201)
      expect(mockPrisma.lLMProfile.create).toHaveBeenCalledWith({
        data: {
          name: newProfile.name,
          model: newProfile.model,
          eloRating: 1500,
        },
      })
    })
  })
})
