import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '../utils'
import ChessDuelArena from '@/components/chess-duel-arena'

// Mock the generateMove function
vi.mock('@/ai/flows/generate-chess-move', () => ({
  generateMove: vi.fn().mockResolvedValue({
    move: 'e4',
    reasoning: 'Opening with king pawn',
    opponentPrediction: 'Expected e5',
    analysis: [],
  }),
}))

// Mock game actions
vi.mock('@/app/actions/history', () => ({
  getGames: vi.fn().mockResolvedValue([]),
  saveGame: vi.fn().mockResolvedValue({}),
  deleteGame: vi.fn().mockResolvedValue({}),
}))

describe('ChessDuelArena', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset window.addGameActivity
    ;(window as any).addGameActivity = vi.fn()
  })

  it('should render chess board and controls', () => {
    render(<ChessDuelArena />)

    expect(screen.getByTestId('chessboard')).toBeInTheDocument()
    expect(screen.getByText('Chess Duel Arena')).toBeInTheDocument()
    expect(screen.getByText('Move History')).toBeInTheDocument()
    expect(screen.getByText('AI Reasoning')).toBeInTheDocument()
  })

  it('should render game mode selection', () => {
    render(<ChessDuelArena />)

    expect(screen.getByText('AI vs AI')).toBeInTheDocument()
    expect(screen.getByText('Human vs AI')).toBeInTheDocument()
    expect(screen.getByText('Freestyle AI vs AI')).toBeInTheDocument()
  })

  it('should render model selection dropdowns', () => {
    render(<ChessDuelArena />)

    expect(screen.getByText('White Player Model')).toBeInTheDocument()
    expect(screen.getByText('Black Player Model')).toBeInTheDocument()
  })

  it('should render navigation buttons', () => {
    render(<ChessDuelArena />)

    expect(screen.getByText('Prev')).toBeInTheDocument()
    expect(screen.getByText('Next')).toBeInTheDocument()
    expect(screen.getByText('Latest')).toBeInTheDocument()
  })

  it('should render copy buttons', () => {
    render(<ChessDuelArena />)

    expect(screen.getByTitle('Copy FEN')).toBeInTheDocument()
    expect(screen.getByTitle('Copy PGN')).toBeInTheDocument()
  })

  it('should show start game button initially', () => {
    render(<ChessDuelArena />)

    expect(screen.getByText('Start Game')).toBeInTheDocument()
  })

  it('should disable navigation buttons when no game is started', () => {
    render(<ChessDuelArena />)

    const prevButton = screen.getByText('Prev')
    const nextButton = screen.getByText('Next')
    const latestButton = screen.getByText('Latest')

    expect(prevButton).toBeDisabled()
    expect(nextButton).toBeDisabled()
    expect(latestButton).toBeDisabled()
  })

  it('should show empty state for move history', () => {
    render(<ChessDuelArena />)

    expect(screen.getByText('No moves yet')).toBeInTheDocument()
  })

  it('should show empty state for AI reasoning', () => {
    render(<ChessDuelArena />)

    expect(screen.getByText('Select a move to view AI reasoning')).toBeInTheDocument()
  })

  it('should handle game mode change', () => {
    render(<ChessDuelArena />)

    const humanVsAiOption = screen.getByLabelText('Human vs AI')
    fireEvent.click(humanVsAiOption)

    expect(humanVsAiOption).toBeChecked()
  })

  it('should handle copy FEN button click', async () => {
    // Mock clipboard API
    const mockWriteText = vi.fn()
    Object.assign(navigator, {
      clipboard: {
        writeText: mockWriteText,
      },
    })

    render(<ChessDuelArena />)

    const copyFenButton = screen.getByTitle('Copy FEN')
    fireEvent.click(copyFenButton)

    expect(mockWriteText).toHaveBeenCalledWith(
      'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1'
    )
  })

  it('should handle copy PGN button click', async () => {
    const mockWriteText = vi.fn()
    Object.assign(navigator, {
      clipboard: {
        writeText: mockWriteText,
      },
    })

    render(<ChessDuelArena />)

    const copyPgnButton = screen.getByTitle('Copy PGN')
    fireEvent.click(copyPgnButton)

    expect(mockWriteText).toHaveBeenCalledWith('')
  })

  it('should handle start game button click', async () => {
    render(<ChessDuelArena />)

    const startButton = screen.getByText('Start Game')
    fireEvent.click(startButton)

    await waitFor(() => {
      expect(screen.queryByText('Start Game')).not.toBeInTheDocument()
    })
  })

  it('should handle reset game button click', async () => {
    render(<ChessDuelArena />)

    // Start a game first
    const startButton = screen.getByText('Start Game')
    fireEvent.click(startButton)

    await waitFor(() => {
      const resetButton = screen.getByText('Reset Game')
      fireEvent.click(resetButton)
    })

    await waitFor(() => {
      expect(screen.getByText('Start Game')).toBeInTheDocument()
    })
  })

  it('should show loading state during AI move', async () => {
    render(<ChessDuelArena />)

    const startButton = screen.getByText('Start Game')
    fireEvent.click(startButton)

    await waitFor(() => {
      expect(screen.getByText('AI Thinking...')).toBeInTheDocument()
    })
  })

  it('should handle spectate mode', () => {
    render(<ChessDuelArena />)
    
    // This test needs to be updated since spectating is handled via URL/state
    expect(screen.getByText('🎮 Chess Duel Arena')).toBeInTheDocument()
  })

  it('should handle autorun mode', async () => {
    render(<ChessDuelArena />)

    const autorunButton = screen.getByText('Autorun')
    fireEvent.click(autorunButton)

    await waitFor(() => {
      expect(screen.getByText('Stop Autorun')).toBeInTheDocument()
    })
  })
})
