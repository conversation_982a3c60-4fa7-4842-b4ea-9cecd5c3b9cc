import { test, expect } from '@playwright/test'

test.describe('Chess Game E2E', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
  })

  test('should load the chess game interface', async ({ page }) => {
    // Check main title
    await expect(page.getByText('Chess Duel Arena')).toBeVisible()
    
    // Check chess board is present
    await expect(page.getByTestId('chessboard')).toBeVisible()
    
    // Check game controls
    await expect(page.getByText('Start Game')).toBeVisible()
    await expect(page.getByText('AI vs AI')).toBeVisible()
    await expect(page.getByText('Human vs AI')).toBeVisible()
  })

  test('should display move history and AI reasoning panels', async ({ page }) => {
    await expect(page.getByText('Move History')).toBeVisible()
    await expect(page.getByText('AI Reasoning')).toBeVisible()
    
    // Check empty states
    await expect(page.getByText('No moves yet')).toBeVisible()
    await expect(page.getByText('Select a move to view AI reasoning')).toBeVisible()
  })

  test('should have navigation controls disabled initially', async ({ page }) => {
    const prevButton = page.getByText('Prev')
    const nextButton = page.getByText('Next')
    const latestButton = page.getByText('Latest')
    
    await expect(prevButton).toBeDisabled()
    await expect(nextButton).toBeDisabled()
    await expect(latestButton).toBeDisabled()
  })

  test('should allow copying FEN and PGN', async ({ page }) => {
    // Grant clipboard permissions
    await page.context().grantPermissions(['clipboard-read', 'clipboard-write'])
    
    // Click copy FEN button
    await page.getByTitle('Copy FEN').click()
    
    // Check if toast appears (assuming toast implementation)
    await expect(page.getByText('Copied to clipboard!')).toBeVisible({ timeout: 5000 })
    
    // Click copy PGN button
    await page.getByTitle('Copy PGN').click()
    await expect(page.getByText('Copied to clipboard!')).toBeVisible({ timeout: 5000 })
  })

  test('should start a game and show AI thinking', async ({ page }) => {
    // Start a game
    await page.getByText('Start Game').click()
    
    // Should show AI thinking state
    await expect(page.getByText('AI Thinking...')).toBeVisible({ timeout: 10000 })
    
    // Game should be in progress
    await expect(page.getByText('Reset Game')).toBeVisible({ timeout: 15000 })
  })

  test('should handle game mode selection', async ({ page }) => {
    // Select Human vs AI mode
    await page.getByText('Human vs AI').click()
    
    // Check if the radio button is selected
    const humanVsAiRadio = page.getByRole('radio', { name: 'Human vs AI' })
    await expect(humanVsAiRadio).toBeChecked()
  })

  test('should handle model selection', async ({ page }) => {
    // Click on white player model dropdown
    await page.getByText('White Player Model').click()
    
    // Select a model (assuming dropdown options are available)
    const modelOption = page.getByText('gemini-2.5-flash').first()
    if (await modelOption.isVisible()) {
      await modelOption.click()
    }
  })

  test('should be responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    // Check if elements are still visible and properly arranged
    await expect(page.getByText('Chess Duel Arena')).toBeVisible()
    await expect(page.getByTestId('chessboard')).toBeVisible()
    await expect(page.getByText('Move History')).toBeVisible()
    await expect(page.getByText('AI Reasoning')).toBeVisible()
  })

  test('should handle autorun functionality', async ({ page }) => {
    // Click autorun button
    await page.getByText('Autorun').click()
    
    // Should show stop autorun button
    await expect(page.getByText('Stop Autorun')).toBeVisible({ timeout: 10000 })
    
    // Should show autorun status
    await expect(page.getByText('Autorunning...')).toBeVisible({ timeout: 5000 })
  })

  test('should handle game reset', async ({ page }) => {
    // Start a game first
    await page.getByText('Start Game').click()
    
    // Wait for game to start
    await expect(page.getByText('Reset Game')).toBeVisible({ timeout: 15000 })
    
    // Reset the game
    await page.getByText('Reset Game').click()
    
    // Should return to initial state
    await expect(page.getByText('Start Game')).toBeVisible()
    await expect(page.getByText('No moves yet')).toBeVisible()
  })

  test('should handle spectate mode', async ({ page }) => {
    // Navigate to spectate mode (assuming URL parameter)
    await page.goto('/?spectate=test-game-id')
    
    // Should show spectating indicator
    await expect(page.getByText('Spectating Game')).toBeVisible({ timeout: 10000 })
  })

  test('should handle network errors gracefully', async ({ page }) => {
    // Intercept API calls and simulate network error
    await page.route('/api/autorun', route => {
      route.abort('failed')
    })
    
    // Try to start a game
    await page.getByText('Start Game').click()
    
    // Should handle error gracefully (check for error message or fallback state)
    // This depends on your error handling implementation
  })

  test('should maintain game state during navigation', async ({ page }) => {
    // Start a game
    await page.getByText('Start Game').click()
    
    // Wait for some moves
    await page.waitForTimeout(5000)
    
    // Navigate to profiles tab
    await page.getByText('Profiles').click()
    
    // Navigate back to game
    await page.getByText('Game').click()
    
    // Game state should be preserved
    await expect(page.getByText('Reset Game')).toBeVisible()
  })
})
